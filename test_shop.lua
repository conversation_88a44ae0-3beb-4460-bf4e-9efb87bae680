-- ========================================
-- CB Gun Shop - Test Script
-- سكربت اختبار سريع
-- ========================================

-- أمر اختبار سريع لفتح المتجر
RegisterCommand("quicktest", function(source, args)
    local userId = vRP.getUserId({source})
    if not userId then return end
    
    print("^2[QUICKTEST] Testing shop for user " .. userId .. "^7")
    
    -- بيانات متجر تجريبي
    local testShopData = {
        action = "openShop",
        shop = {
            id = "test_shop",
            name = "متجر الاختبار",
            coords = vector3(0, 0, 0),
            type = "normal"
        },
        weapons = {
            {
                name = "weapon_pistol",
                label = "مسدس تجريبي",
                price = 1000,
                image = "weapon_pistol.png",
                category = "handguns",
                description = "مسدس للاختبار",
                damage = 25,
                accuracy = 50,
                range = 25
            },
            {
                name = "weapon_knife",
                label = "سكين تجريبي",
                price = 500,
                image = "weapon_knife.png",
                category = "melee",
                description = "سكين للاختبار",
                damage = 100,
                accuracy = 100,
                range = 5
            },
            {
                name = "weapon_bat",
                label = "مضرب تجريبي",
                price = 300,
                image = "weapon_bat.png",
                category = "melee",
                description = "مضرب للاختبار",
                damage = 80,
                accuracy = 90,
                range = 10
            }
        },
        playerMoney = 50000,
        serverInfo = {
            name = "CB Store Test",
            logo = "https://via.placeholder.com/60x60"
        }
    }
    
    print("^3[QUICKTEST] Sending test data to client...^7")
    TriggerClientEvent("cb_gunshop:openUI", source, testShopData)
    
    print("^2[QUICKTEST] Test shop opened successfully!^7")
end, false)

-- أمر لاختبار NUI مباشرة
RegisterCommand("testnui2", function(source, args)
    print("^3[QUICKTEST] Testing NUI communication...^7")
    
    TriggerClientEvent("cb_gunshop:testNUI", source)
    
    print("^2[QUICKTEST] NUI test sent!^7")
end, false)

-- أمر لطباعة معلومات التطوير
RegisterCommand("debuginfo", function(source, args)
    local userId = vRP.getUserId({source})
    if not userId then return end
    
    print("^2[DEBUG INFO] User ID: " .. userId .. "^7")
    print("^2[DEBUG INFO] Source: " .. source .. "^7")
    
    -- تحقق من vRP
    if vRP then
        print("^2[DEBUG INFO] vRP loaded: YES^7")
        local money = vRP.getMoney({userId})
        local bank = vRP.getBankMoney({userId})
        print("^2[DEBUG INFO] Money: " .. (money or 0) .. "^7")
        print("^2[DEBUG INFO] Bank: " .. (bank or 0) .. "^7")
    else
        print("^1[DEBUG INFO] vRP loaded: NO^7")
    end
    
    -- تحقق من Config
    if Config then
        print("^2[DEBUG INFO] Config loaded: YES^7")
        if Config.Shops then
            print("^2[DEBUG INFO] Shops count: " .. #Config.Shops .. "^7")
        end
        if Config.WeaponDatabase then
            print("^2[DEBUG INFO] Weapons count: " .. #Config.WeaponDatabase .. "^7")
        end
    else
        print("^1[DEBUG INFO] Config loaded: NO^7")
    end
    
    TriggerClientEvent('chatMessage', source, "^2[DEBUG]^7", {255, 255, 255}, "معلومات التطوير تم طباعتها في وحدة التحكم")
end, false)

print("^2[QUICKTEST] Test commands loaded:^7")
print("^3/quicktest^7 - فتح متجر تجريبي")
print("^3/testnui2^7 - اختبار NUI")
print("^3/debuginfo^7 - معلومات التطوير")
