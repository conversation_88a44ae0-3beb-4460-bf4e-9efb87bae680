let currentWeapons = {};
let playerMoney = 0;
let currentCategory = 'pistol';

// استقبال الرسائل من اللعبة
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'open':
            openShop(data);
            break;
        case 'close':
            closeShop();
            break;
        case 'purchaseResult':
            handlePurchaseResult(data);
            break;
    }
});

// فتح المتجر
function openShop(data) {
    currentWeapons = data.weapons;
    playerMoney = data.money;
    
    document.getElementById('shopName').textContent = data.shopName || 'متجر الأسلحة';
    document.getElementById('playerMoney').textContent = playerMoney.toLocaleString();
    document.getElementById('container').classList.remove('hidden');
    
    displayWeapons(currentCategory);
}

// إغلاق المتجر
function closeShop() {
    document.getElementById('container').classList.add('hidden');
    fetch(`https://${GetParentResourceName()}/close`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({})
    });
}

// عرض الأسلحة
function displayWeapons(category) {
    const container = document.getElementById('weaponsContainer');
    container.innerHTML = '';
    
    if (!currentWeapons[category]) return;
    
    currentWeapons[category].forEach(weapon => {
        const weaponCard = createWeaponCard(weapon);
        container.appendChild(weaponCard);
    });
}

// إنشاء كارت السلاح
function createWeaponCard(weapon) {
    const card = document.createElement('div');
    card.className = 'weapon-card';
    
    card.innerHTML = `
        <div class="weapon-name">${weapon.name}</div>
        <div class="weapon-price">${weapon.price.toLocaleString()}$</div>
        <div class="ammo-section">
            <label>كمية الذخيرة:</label>
            <input type="number" class="ammo-input" value="50" min="0" max="250">
        </div>
        <button class="buy-btn" onclick="buyWeapon('${weapon.hash}', '${weapon.name}', ${weapon.price}, this)">
            شراء السلاح
        </button>
    `;
    
    return card;
}

// شراء سلاح
function buyWeapon(hash, name, price, button) {
    const ammoInput = button.parentElement.querySelector('.ammo-input');
    const ammo = parseInt(ammoInput.value) || 0;
    
    if (ammo < 0 || ammo > 250) {
        showAlert('كمية الذخيرة يجب أن تكون بين 0 و 250', 'error');
        return;
    }
    
    const totalPrice = price + (ammo * 10); // 10$ لكل رصاصة
    
    if (totalPrice > playerMoney) {
        showAlert('لا تملك مال كافي!', 'error');
        return;
    }
    
    button.disabled = true;
    button.textContent = 'جاري الشراء...';
    
    fetch(`https://${GetParentResourceName()}/buyWeapon`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            weapon: {hash: hash, name: name, price: price},
            ammo: ammo
        })
    });
}

// معالجة نتيجة الشراء
function handlePurchaseResult(data) {
    if (data.success) {
        playerMoney = data.money;
        document.getElementById('playerMoney').textContent = playerMoney.toLocaleString();
        showAlert(data.message, 'success');
    } else {
        showAlert(data.message, 'error');
    }
    
    // إعادة تفعيل الأزرار
    document.querySelectorAll('.buy-btn').forEach(btn => {
        btn.disabled = false;
        btn.textContent = 'شراء السلاح';
    });
}

// عرض التنبيهات
function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.textContent = message;
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// الأحداث
document.getElementById('closeBtn').addEventListener('click', closeShop);

// أزرار الفئات
document.querySelectorAll('.category-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        currentCategory = this.dataset.category;
        displayWeapons(currentCategory);
    });
});

// إغلاق بالـ ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeShop();
    }
});

// دالة مساعدة للحصول على اسم المورد
function GetParentResourceName() {
    return window.location.hostname;
}