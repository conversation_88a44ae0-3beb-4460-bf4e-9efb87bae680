local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

-- VRP Interface
vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP")

-- Variables
local purchaseLog = {}
local playerShopData = {}

-- Wait for vRP to load
Citizen.CreateThread(function()
    while not vRP do
        Citizen.Wait(100)
    end
    print("^2[CB_GunShop]^7 Server loaded successfully!")
end)

-- Utility Functions
function GetPlayerMoney(user_id)
    if not vRP then return 0 end
    local money = vRP.getMoney({user_id}) or 0
    local bankMoney = vRP.getBankMoney({user_id}) or 0
    return money + bankMoney
end

function TryPayment(user_id, amount)
    if not vRP then return false end
    return vRP.tryFullPayment({user_id, amount}) or false
end

function GiveWeapon(source, weapon, ammo)
    if not vRPclient then return false end
    
    local weapons = {}
    weapons[weapon] = {ammo = ammo}
    vRPclient.giveWeapons(source, {weapons}, false)
    return true
end

function HasPermission(user_id, group)
    if not vRP then return true end
    if group == "user" then return true end
    return vRP.hasGroup({user_id, group}) or false
end

function ValidateWeapon(weaponHash)
    for category, data in pairs(Config.Weapons) do
        for _, weapon in pairs(data.weapons) do
            if weapon.hash == weaponHash then
                return weapon
            end
        end
    end
    return nil
end

function LogPurchase(user_id, source, weapon, price, ammo)
    local playerName = GetPlayerName(source) or "Unknown"
    local logEntry = {
        user_id = user_id,
        player_name = playerName,
        weapon = weapon.name,
        weapon_hash = weapon.hash,
        price = price,
        ammo = ammo,
        timestamp = os.time(),
        date = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    table.insert(purchaseLog, logEntry)
    
    print(string.format("^3[CB_GunShop]^7 Purchase Log: %s (ID: %d) bought %s for %d$ with %d ammo", 
        playerName, user_id, weapon.name, price, ammo))
end

function IsValidShop(shopId)
    return Config.Shops[shopId] ~= nil
end

function GetShopById(shopId)
    return Config.Shops[shopId]
end

-- Events
RegisterServerEvent('gunshop:requestOpen')
AddEventHandler('gunshop:requestOpen', function(shopId)
    local source = source
    
    if not vRP then
        print("^1[CB_GunShop]^7 vRP not loaded")
        return
    end
    
    local user_id = vRP.getUserId({source})
    if not user_id then
        print("^1[CB_GunShop]^7 Could not get user_id for player " .. source)
        return
    end
    
    -- Validate shop
    if not IsValidShop(shopId) then
        TriggerClientEvent('gunshop:notification', source, "متجر غير صحيح", "error")
        return
    end
    
    local shop = GetShopById(shopId)
    
    -- Check permissions
    local hasPermission = true
    for _, group in pairs(Config.AllowedGroups) do
        if HasPermission(user_id, group) then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        TriggerClientEvent('gunshop:notification', source, "لا تملك صلاحية لاستخدام هذا المتجر", "error")
        return
    end
    
    -- Get player money
    local playerMoney = GetPlayerMoney(user_id)
    
    -- Store player shop data
    playerShopData[source] = {
        user_id = user_id,
        shop_id = shopId,
        opened_at = os.time()
    }
    
    -- Send shop data to client
    TriggerClientEvent('gunshop:openUI', source, playerMoney, Config.Weapons, shop)
    
    print(string.format("^2[CB_GunShop]^7 %s (ID: %d) opened shop %s", 
        GetPlayerName(source), user_id, shop.name))
end)

RegisterServerEvent('gunshop:buyWeapon')
AddEventHandler('gunshop:buyWeapon', function(data)
    local source = source
    
    if not vRP then
        TriggerClientEvent('gunshop:purchaseResult', source, false, "خطأ في النظام", 0)
        return
    end
    
    local user_id = vRP.getUserId({source})
    if not user_id then
        TriggerClientEvent('gunshop:purchaseResult', source, false, "خطأ في تحديد الهوية", 0)
        return
    end
    
    -- Validate player shop data
    if not playerShopData[source] then
        TriggerClientEvent('gunshop:purchaseResult', source, false, "يجب فتح المتجر أولاً", 0)
        return
    end
    
    -- Validate input data
    if not data or not data.weapon or not data.ammo or not data.shopId then
        TriggerClientEvent('gunshop:purchaseResult', source, false, "بيانات غير صحيحة", 0)
        return
    end
    
    -- Validate shop
    if not IsValidShop(data.shopId) then
        TriggerClientEvent('gunshop:purchaseResult', source, false, "متجر غير صحيح", 0)
        return
    end
    
    -- Validate weapon
    local weapon = ValidateWeapon(data.weapon.hash)
    if not weapon then
        TriggerClientEvent('gunshop:purchaseResult', source, false, Config.Messages['invalid_weapon'], 0)
        return
    end
    
    -- Validate ammo
    local ammo = tonumber(data.ammo) or Config.DefaultAmmo
    if ammo < 0 then ammo = 0 end
    if ammo > Config.MaxAmmo then ammo = Config.MaxAmmo end
    
    -- Calculate prices
    local weaponPrice = tonumber(weapon.price) or 0
    local ammoPrice = ammo * Config.AmmoPrice
    local totalPrice = weaponPrice + ammoPrice
    
    -- Check money
    local playerMoney = GetPlayerMoney(user_id)
    if playerMoney < totalPrice then
        TriggerClientEvent('gunshop:purchaseResult', source, false, 
            string.format("لا تملك مال كافي! تحتاج %d%s", totalPrice, Config.Currency), playerMoney)
        return
    end
    
    -- Process payment
    if TryPayment(user_id, totalPrice) then
        -- Give weapon
        if GiveWeapon(source, weapon.hash, ammo) then
            local newMoney = GetPlayerMoney(user_id)
            
            -- Log purchase
            LogPurchase(user_id, source, weapon, totalPrice, ammo)
            
            -- Send success response
            TriggerClientEvent('gunshop:purchaseResult', source, true, 
                string.format("تم شراء %s بنجاح مقابل %d%s", weapon.name, totalPrice, Config.Currency), newMoney)
            
            print(string.format("^2[CB_GunShop]^7 Successful purchase: %s (ID: %d) bought %s for %d%s with %d ammo", 
                GetPlayerName(source), user_id, weapon.name, totalPrice, Config.Currency, ammo))
        else
            -- Refund if weapon giving failed
            if vRP then
                vRP.giveMoney({user_id, totalPrice})
            end
            TriggerClientEvent('gunshop:purchaseResult', source, false, "فشل في إعطاء السلاح", playerMoney)
        end
    else
        TriggerClientEvent('gunshop:purchaseResult', source, false, "فشل في خصم المال", playerMoney)
    end
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source
    if playerShopData[source] then
        playerShopData[source] = nil
    end
end)

-- Admin Commands
RegisterCommand('gunshop_logs', function(source, args, rawCommand)
    if source == 0 then -- Console only
        print("^3[CB_GunShop]^7 Recent Purchase Logs:")
        print("^3=====================================^7")
        
        local recentLogs = {}
        for i = math.max(1, #purchaseLog - 9), #purchaseLog do
            if purchaseLog[i] then
                table.insert(recentLogs, purchaseLog[i])
            end
        end
        
        for _, log in pairs(recentLogs) do
            print(string.format("^7[%s] %s (ID: %d) - %s - %d%s - %d ammo", 
                log.date, log.player_name, log.user_id, log.weapon, log.price, Config.Currency, log.ammo))
        end
        
        print("^3=====================================^7")
        print(string.format("^7Total purchases logged: %d", #purchaseLog))
    else
        local user_id = vRP.getUserId({source})
        if user_id and vRP.hasPermission({user_id, "admin.tickets"}) then
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 255},
                multiline = true,
                args = {"نظام الأسلحة", string.format("تم تسجيل %d عملية شراء. تحقق من الكونسول للتفاصيل.", #purchaseLog)}
            })
        end
    end
end, false)

RegisterCommand('gunshop_reload', function(source, args, rawCommand)
    if source == 0 then -- Console only
        -- Reload config (if needed)
        print("^2[CB_GunShop]^7 Configuration reloaded!")
    else
        local user_id = vRP.getUserId({source})
        if user_id and vRP.hasPermission({user_id, "admin.tickets"}) then
            TriggerClientEvent('chat:addMessage', source, {
                color = {0, 255, 0},
                multiline = true,
                args = {"نظام الأسلحة", "تم إعادة تحميل الإعدادات!"}
            })
        end
    end
end, false)

-- Export functions
exports('GetPurchaseLogs', function()
    return purchaseLog
end)

exports('GetPlayerShopData', function(source)
    return playerShopData[source]
end)

exports('GetShopWeapons', function()
    return Config.Weapons
end)

-- Resource stop cleanup
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Clear player shop data
        playerShopData = {}
        print("^3[CB_GunShop]^7 Server cleanup completed!")
    end
end)