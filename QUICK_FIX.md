# 🚨 حل سريع لمشكلة عدم ظهور المتاجر

## المشكلة
- لا توجد مواقع أسلحة
- لا يمكن فتح المتجر

## الحل السريع (5 دقائق)

### 1. إعادة تشغيل المورد
```bash
# في وحدة تحكم الخادم
restart CB_GunShop
```

### 2. التحقق من الأخطاء
```bash
# في وحدة تحكم الخادم
monitor
```

### 3. إذا لم يعمل، اتبع هذه الخطوات:

#### أ) تحقق من وجود الملفات
تأكد من وجود هذه الملفات:
- ✅ `new_config.lua`
- ✅ `new_client.lua` 
- ✅ `new_server.lua`
- ✅ `fxmanifest.lua` (الجديد)
- ✅ `new_ui/index.html`

#### ب) إضافة صور الأسلحة
```bash
# أنشئ مجلد الصور إذا لم يكن موجود
mkdir new_ui/img

# ضع صور الأسلحة هنا:
# weapon_pistol.png
# weapon_assaultrifle.png
# weapon_knife.png
# إلخ...
```

#### ج) تحديث الصلاحيات في vRP
```lua
-- في groups.lua
["citizen"] = {
    "citizen.basic",
    "gunshop.access"
},

["police"] = {
    "police.officer",
    "police.armory"
},

["admin"] = {
    "server.owner"
}
```

### 4. إعادة تشغيل الخادم
```bash
# أعد تشغيل الخادم كاملاً
restart
```

## 🎯 مواقع المتاجر الافتراضية

بعد التشغيل، ستجد المتاجر في هذه المواقع:

### 1. متجر وسط المدينة
- **الموقع**: `45.21, -1747.91, 29.54`
- **الصلاحية**: `gunshop.access`
- **الأسلحة**: جميع الأسلحة

### 2. مخزن الشرطة  
- **الموقع**: `451.51, -979.44, 30.68`
- **الصلاحية**: `police.armory`
- **الأسلحة**: مسدسات، بنادق، رشاشات

### 3. السوق السوداء
- **الموقع**: `707.84, -966.87, 30.41`
- **الصلاحية**: `blackmarket.access`
- **الأسلحة**: أسلحة متقدمة

### 4. متجر VIP
- **الموقع**: `-662.15, -935.3, 21.82`
- **الصلاحية**: `vip.premium`
- **الأسلحة**: جميع الأسلحة مع خصم

### 5. متجر المطار
- **الموقع**: `-2130.70, -570.32, 18.25`
- **الصلاحية**: `vip.premium`
- **الأسلحة**: قناصة، رشاشات ثقيلة

## 🔍 التحقق من عمل النظام

### 1. ابحث عن البليبات
- يجب أن ترى أيقونات متاجر على الخريطة
- لون مختلف لكل نوع متجر

### 2. اقترب من المتجر
- يجب أن ترى ماركر أبيض
- نص "اضغط [E] للدخول"

### 3. اضغط E
- يجب أن تفتح واجهة المتجر
- تظهر الأسلحة المتاحة

## ⚠️ إذا لم يعمل النظام

### تحقق من هذه النقاط:

1. **الصلاحيات**
   ```lua
   -- تأكد من إضافة هذه الصلاحية للاعب
   /addgroup [ID] citizen
   ```

2. **ساعات العمل**
   ```lua
   -- بعض المتاجر لها ساعات عمل محددة
   -- السوق السوداء: 22:00 - 06:00
   -- المتاجر العادية: 06:00 - 22:00
   ```

3. **المسافة**
   ```lua
   -- تأكد من أنك قريب بما فيه الكفاية
   -- المسافة المطلوبة: 2.5 متر
   ```

## 🆘 الدعم الطارئ

إذا لم تعمل الحلول أعلاه:

1. **تحقق من وحدة التحكم**
   ```bash
   # ابحث عن أخطاء مثل:
   # "SCRIPT ERROR"
   # "Failed to load"
   # "Permission denied"
   ```

2. **تأكد من إصدار vRP**
   ```lua
   -- النظام يتطلب vRP 2.0 أو أحدث
   ```

3. **تحقق من قاعدة البيانات**
   ```sql
   -- تأكد من اتصال MySQL
   ```

## 📞 اتصل بالدعم

إذا استمرت المشكلة، أرسل هذه المعلومات:
- إصدار FiveM
- إصدار vRP  
- رسائل الخطأ من وحدة التحكم
- لقطة شاشة من مجلد الموارد

---

## 🔧 حل مشكلة عدم ظهور الأسلحة في المتجر

### المشكلة الجديدة:
المتجر يفتح ولكن لا تظهر الأسلحة أو القوائم

### الحلول الفورية:

#### 1. أوامر الاختبار:
```
/testshop    # فتح متجر تجريبي
/testnui     # اختبار الواجهة
```

#### 2. إضافة ملف التطوير:
أضف `debug_fix.lua` إلى `fxmanifest.lua`:
```lua
server_scripts {
    'debug_fix.lua'  -- أضف هذا السطر
}
```

#### 3. تحقق من البيانات:
تأكد من وجود أسلحة في `new_config.lua`:
```lua
Config.WeaponDatabase = {
    {name = "weapon_pistol", label = "مسدس", price = 1000}
}
```

#### 4. رسائل التطوير:
ابحث عن رسائل `[DEBUG]` في وحدة التحكم

#### 5. إعادة تشغيل كاملة:
```
restart CB_GunShop
refresh
```

---

**ملاحظة**: هذا النظام الجديد مختلف تماماً عن القديم ويتطلب إعداد جديد!
