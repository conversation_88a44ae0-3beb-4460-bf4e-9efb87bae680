-- ========================================
-- CB Gun Shop - Security System
-- نظام الأمان ومنع الاستغلال
-- ========================================

local Security = {}
local ErrorHandler = require('lib/error_handler')
local Logger = require('lib/logger')
local Utils = require('lib/utils')
local Constants = require('lib/constants')

-- ========================================
-- إعدادات الأمان
-- ========================================

local SECURITY_CONFIG = {
    MAX_REQUESTS_PER_MINUTE = 30,       -- أقصى عدد طلبات في الدقيقة
    MAX_PURCHASE_VALUE = 1000000,       -- أقصى قيمة شراء
    MIN_PURCHASE_INTERVAL = 2,          -- أقل فترة بين المشتريات (ثانية)
    MAX_FAILED_ATTEMPTS = 5,            -- أقصى عدد محاولات فاشلة
    BLACKLIST_DURATION = 3600,          -- مدة الحظر (ثانية)
    RATE_LIMIT_WINDOW = 60,             -- نافزة تحديد المعدل (ثانية)
    SUSPICIOUS_THRESHOLD = 10,          -- عتبة النشاط المشبوه
    AUTO_BAN_ENABLED = false            -- الحظر التلقائي
}

-- ========================================
-- متغيرات النظام
-- ========================================

local playerRequests = {}              -- طلبات اللاعبين
local playerFailures = {}              -- فشل اللاعبين
local blacklistedPlayers = {}          -- اللاعبون المحظورون
local suspiciousActivity = {}          -- النشاط المشبوه
local lastPurchaseTime = {}            -- آخر وقت شراء

-- ========================================
-- دوال التحقق الأساسية
-- ========================================

-- التحقق من معدل الطلبات
function Security.CheckRateLimit(userId)
    local currentTime = os.time()
    
    if not playerRequests[userId] then
        playerRequests[userId] = {}
    end
    
    -- تنظيف الطلبات القديمة
    local validRequests = {}
    for _, requestTime in ipairs(playerRequests[userId]) do
        if currentTime - requestTime < SECURITY_CONFIG.RATE_LIMIT_WINDOW then
            table.insert(validRequests, requestTime)
        end
    end
    
    playerRequests[userId] = validRequests
    
    -- التحقق من تجاوز الحد
    if #playerRequests[userId] >= SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE then
        Security.LogSuspiciousActivity(userId, "rate_limit_exceeded", {
            requestCount = #playerRequests[userId],
            timeWindow = SECURITY_CONFIG.RATE_LIMIT_WINDOW
        })
        return false
    end
    
    -- إضافة الطلب الحالي
    table.insert(playerRequests[userId], currentTime)
    return true
end

-- التحقق من فترة الشراء
function Security.CheckPurchaseInterval(userId)
    local currentTime = os.time()
    local lastTime = lastPurchaseTime[userId]
    
    if lastTime and (currentTime - lastTime) < SECURITY_CONFIG.MIN_PURCHASE_INTERVAL then
        Security.LogSuspiciousActivity(userId, "purchase_too_fast", {
            interval = currentTime - lastTime,
            minInterval = SECURITY_CONFIG.MIN_PURCHASE_INTERVAL
        })
        return false
    end
    
    lastPurchaseTime[userId] = currentTime
    return true
end

-- التحقق من القائمة السوداء
function Security.IsBlacklisted(userId)
    local blacklistEntry = blacklistedPlayers[userId]
    
    if not blacklistEntry then
        return false
    end
    
    -- التحقق من انتهاء مدة الحظر
    if os.time() > blacklistEntry.expiresAt then
        blacklistedPlayers[userId] = nil
        Logger.LogSecurityEvent(userId, "blacklist_expired", "info", {
            reason = blacklistEntry.reason,
            duration = blacklistEntry.duration
        })
        return false
    end
    
    return true, blacklistEntry.reason
end

-- ========================================
-- دوال التحقق من صحة البيانات
-- ========================================

-- التحقق من صحة بيانات الشراء
function Security.ValidatePurchaseData(data, userId)
    local errors = {}
    
    -- التحقق من وجود البيانات الأساسية
    if not data.weapon or type(data.weapon) ~= "string" then
        table.insert(errors, "invalid_weapon_name")
    end
    
    if not data.price or type(data.price) ~= "number" or data.price < 0 then
        table.insert(errors, "invalid_price")
    end
    
    if not data.shopId or type(data.shopId) ~= "string" then
        table.insert(errors, "invalid_shop_id")
    end
    
    -- التحقق من كمية الذخيرة
    if data.ammo then
        local ammo = tonumber(data.ammo)
        if not ammo or ammo < 0 or ammo > Config.Weapons.MaxAmmo then
            table.insert(errors, "invalid_ammo_count")
        end
    end
    
    -- التحقق من قيمة الشراء
    if data.price and data.price > SECURITY_CONFIG.MAX_PURCHASE_VALUE then
        table.insert(errors, "purchase_value_too_high")
        Security.LogSuspiciousActivity(userId, "high_value_purchase", {
            price = data.price,
            maxAllowed = SECURITY_CONFIG.MAX_PURCHASE_VALUE
        })
    end
    
    -- التحقق من وجود السلاح في قاعدة البيانات
    if data.weapon then
        local weaponExists = false
        for _, weapon in ipairs(Config.WeaponDatabase) do
            if weapon.name == data.weapon then
                weaponExists = true
                
                -- التحقق من السعر
                local priceDifference = math.abs(data.price - weapon.price)
                local tolerance = weapon.price * 0.1 -- هامش خطأ 10%
                
                if priceDifference > tolerance then
                    table.insert(errors, "price_manipulation")
                    Security.LogSuspiciousActivity(userId, "price_manipulation", {
                        weapon = data.weapon,
                        sentPrice = data.price,
                        correctPrice = weapon.price,
                        difference = priceDifference
                    })
                end
                
                break
            end
        end
        
        if not weaponExists then
            table.insert(errors, "weapon_not_found")
        end
    end
    
    return #errors == 0, errors
end

-- التحقق من صحة بيانات المتجر
function Security.ValidateShopData(shopData, userId)
    local errors = {}
    
    if not shopData or type(shopData) ~= "table" then
        table.insert(errors, "invalid_shop_data")
        return false, errors
    end
    
    if not shopData.id or type(shopData.id) ~= "string" then
        table.insert(errors, "invalid_shop_id")
    end
    
    -- التحقق من وجود المتجر
    if shopData.id then
        local shopExists = false
        for _, shop in ipairs(Config.Shops) do
            if shop.id == shopData.id then
                shopExists = true
                break
            end
        end
        
        if not shopExists then
            table.insert(errors, "shop_not_found")
            Security.LogSuspiciousActivity(userId, "invalid_shop_access", {
                shopId = shopData.id
            })
        end
    end
    
    return #errors == 0, errors
end

-- ========================================
-- دوال مكافحة الغش
-- ========================================

-- فحص شامل لمكافحة الغش
function Security.AntiCheatCheck(userId, action, data)
    local violations = {}
    
    -- فحص معدل الطلبات
    if not Security.CheckRateLimit(userId) then
        table.insert(violations, "rate_limit_violation")
    end
    
    -- فحص فترة الشراء
    if action == "purchase" and not Security.CheckPurchaseInterval(userId) then
        table.insert(violations, "purchase_interval_violation")
    end
    
    -- فحص القائمة السوداء
    local isBlacklisted, reason = Security.IsBlacklisted(userId)
    if isBlacklisted then
        table.insert(violations, "blacklisted_user")
        Logger.LogSecurityEvent(userId, "blacklist_access_attempt", "high", {
            reason = reason,
            action = action
        })
    end
    
    -- فحص البيانات حسب نوع العملية
    if action == "purchase" then
        local isValid, errors = Security.ValidatePurchaseData(data, userId)
        if not isValid then
            for _, error in ipairs(errors) do
                table.insert(violations, error)
            end
        end
    elseif action == "shop_access" then
        local isValid, errors = Security.ValidateShopData(data, userId)
        if not isValid then
            for _, error in ipairs(errors) do
                table.insert(violations, error)
            end
        end
    end
    
    -- تسجيل المخالفات
    if #violations > 0 then
        Security.RecordViolations(userId, action, violations, data)
        return false, violations
    end
    
    return true, {}
end

-- تسجيل المخالفات
function Security.RecordViolations(userId, action, violations, data)
    -- تحديث عداد الفشل
    if not playerFailures[userId] then
        playerFailures[userId] = {
            count = 0,
            lastFailure = 0,
            violations = {}
        }
    end
    
    playerFailures[userId].count = playerFailures[userId].count + 1
    playerFailures[userId].lastFailure = os.time()
    
    for _, violation in ipairs(violations) do
        playerFailures[userId].violations[violation] = (playerFailures[userId].violations[violation] or 0) + 1
    end
    
    -- تسجيل الحدث الأمني
    Logger.LogSecurityEvent(userId, "anticheat_violation", "high", {
        action = action,
        violations = violations,
        failureCount = playerFailures[userId].count,
        data = data
    })
    
    -- التحقق من الحظر التلقائي
    if SECURITY_CONFIG.AUTO_BAN_ENABLED and playerFailures[userId].count >= SECURITY_CONFIG.MAX_FAILED_ATTEMPTS then
        Security.BlacklistPlayer(userId, "auto_ban_violations", SECURITY_CONFIG.BLACKLIST_DURATION)
    end
end

-- ========================================
-- دوال إدارة القائمة السوداء
-- ========================================

-- إضافة لاعب للقائمة السوداء
function Security.BlacklistPlayer(userId, reason, duration)
    local expiresAt = os.time() + (duration or SECURITY_CONFIG.BLACKLIST_DURATION)
    
    blacklistedPlayers[userId] = {
        reason = reason,
        addedAt = os.time(),
        expiresAt = expiresAt,
        duration = duration or SECURITY_CONFIG.BLACKLIST_DURATION
    }
    
    Logger.LogSecurityEvent(userId, "player_blacklisted", "critical", {
        reason = reason,
        duration = duration,
        expiresAt = expiresAt
    })
    
    ErrorHandler.Critical("Player blacklisted", {
        userId = userId,
        reason = reason,
        duration = duration
    }, "Security")
end

-- إزالة لاعب من القائمة السوداء
function Security.RemoveFromBlacklist(userId, reason)
    if blacklistedPlayers[userId] then
        blacklistedPlayers[userId] = nil
        
        Logger.LogSecurityEvent(userId, "blacklist_removed", "info", {
            reason = reason
        })
    end
end

-- ========================================
-- دوال النشاط المشبوه
-- ========================================

-- تسجيل نشاط مشبوه
function Security.LogSuspiciousActivity(userId, activityType, details)
    if not suspiciousActivity[userId] then
        suspiciousActivity[userId] = {}
    end
    
    table.insert(suspiciousActivity[userId], {
        type = activityType,
        details = details,
        timestamp = os.time()
    })
    
    Logger.LogSecurityEvent(userId, "suspicious_activity", "medium", {
        activityType = activityType,
        details = details,
        totalSuspiciousActivities = #suspiciousActivity[userId]
    })
    
    -- التحقق من تجاوز العتبة
    if #suspiciousActivity[userId] >= SECURITY_CONFIG.SUSPICIOUS_THRESHOLD then
        Security.BlacklistPlayer(userId, "suspicious_activity_threshold", SECURITY_CONFIG.BLACKLIST_DURATION)
    end
end

-- ========================================
-- دوال الإحصائيات والتقارير
-- ========================================

-- الحصول على إحصائيات الأمان
function Security.GetSecurityStats()
    local stats = {
        blacklistedPlayers = 0,
        suspiciousActivities = 0,
        totalViolations = 0,
        activeThreats = 0
    }
    
    -- عد اللاعبين المحظورين
    for userId, _ in pairs(blacklistedPlayers) do
        stats.blacklistedPlayers = stats.blacklistedPlayers + 1
    end
    
    -- عد الأنشطة المشبوهة
    for userId, activities in pairs(suspiciousActivity) do
        stats.suspiciousActivities = stats.suspiciousActivities + #activities
    end
    
    -- عد المخالفات
    for userId, failures in pairs(playerFailures) do
        stats.totalViolations = stats.totalViolations + failures.count
        
        -- التهديدات النشطة (فشل في آخر ساعة)
        if os.time() - failures.lastFailure < 3600 then
            stats.activeThreats = stats.activeThreats + 1
        end
    end
    
    return stats
end

-- تنظيف البيانات القديمة
function Security.CleanupOldData()
    local currentTime = os.time()
    local oneHourAgo = currentTime - 3600
    
    -- تنظيف طلبات اللاعبين
    for userId, requests in pairs(playerRequests) do
        local validRequests = {}
        for _, requestTime in ipairs(requests) do
            if requestTime > oneHourAgo then
                table.insert(validRequests, requestTime)
            end
        end
        playerRequests[userId] = validRequests
    end
    
    -- تنظيف الأنشطة المشبوهة القديمة
    for userId, activities in pairs(suspiciousActivity) do
        local validActivities = {}
        for _, activity in ipairs(activities) do
            if activity.timestamp > oneHourAgo then
                table.insert(validActivities, activity)
            end
        end
        suspiciousActivity[userId] = validActivities
    end
end

-- ========================================
-- تهيئة النظام
-- ========================================

-- تهيئة نظام الأمان
function Security.Initialize()
    -- تنظيف دوري للبيانات
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(300000) -- كل 5 دقائق
            Security.CleanupOldData()
        end
    end)
    
    Logger.Log("system", "security_initialized", {
        maxRequestsPerMinute = SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE,
        autoBanEnabled = SECURITY_CONFIG.AUTO_BAN_ENABLED
    })
end

-- ========================================
-- تصدير النظام
-- ========================================

return Security
