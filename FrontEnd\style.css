* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Arial', sans-serif;
    background: transparent;
    color: #ffffff;
    direction: rtl;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

/* Container */
#container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.shop-wrapper {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 20px;
    width: 95%;
    max-width: 1200px;
    height: 90vh;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header */
.shop-header {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.server-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
}

.shop-info h1 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.shop-info p {
    font-size: 14px;
    opacity: 0.8;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.money-display {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.money-icon {
    font-size: 18px;
}

.money-amount {
    font-size: 18px;
    font-weight: 700;
    color: #2ecc71;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Categories */
.categories-container {
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.categories-wrapper {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.category-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    padding: 12px 25px;
    border-radius: 15px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.category-btn:hover {
    background: rgba(231, 76, 60, 0.3);
    border-color: #e74c3c;
    transform: translateY(-2px);
}

.category-btn.active {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-color: #e74c3c;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.category-icon {
    font-size: 16px;
}

/* Weapons Section */
.weapons-section {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(231, 76, 60, 0.3);
}

.section-header h2 {
    font-size: 22px;
    font-weight: 700;
    color: #e74c3c;
}

.weapons-count {
    background: rgba(231, 76, 60, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.weapons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding-bottom: 20px;
}

/* Weapon Cards */
.weapon-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.weapon-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e74c3c, #c0392b);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.weapon-card:hover::before {
    transform: scaleX(1);
}

.weapon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.2);
    border-color: rgba(231, 76, 60, 0.5);
}

.weapon-image {
    width: 100%;
    height: 120px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: #e74c3c;
    border: 2px solid rgba(231, 76, 60, 0.2);
}

.weapon-info {
    text-align: center;
    margin-bottom: 20px;
}

.weapon-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #ffffff;
}

.weapon-description {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    line-height: 1.4;
}

.weapon-price {
    font-size: 20px;
    font-weight: 700;
    color: #2ecc71;
    margin-bottom: 15px;
}

.buy-btn {
    width: 100%;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.buy-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.buy-btn:hover::before {
    left: 100%;
}

.buy-btn:hover {
    background: linear-gradient(45deg, #27ae60, #229954);
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
}

.buy-btn:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
}

.modal-header {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 30px;
}

.weapon-preview {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;
}

.weapon-preview img {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    border: 2px solid rgba(231, 76, 60, 0.3);
}

.weapon-info h4 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #e74c3c;
}

.weapon-info p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

.ammo-selection {
    margin-bottom: 25px;
}

.ammo-selection label {
    display: block;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
}

.slider-container {
    position: relative;
}

#ammoSlider {
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    -webkit-appearance: none;
}

#ammoSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e74c3c;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.5);
}

#ammoSlider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e74c3c;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.5);
}

.slider-values {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

#ammoValue {
    color: #e74c3c;
    font-weight: 700;
}

.price-breakdown {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 16px;
}

.price-row.total {
    border-top: 2px solid rgba(231, 76, 60, 0.3);
    padding-top: 15px;
    margin-top: 15px;
    font-weight: 700;
    font-size: 18px;
    color: #2ecc71;
}

.modal-footer {
    padding: 20px 30px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-confirm {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    border: none;
    padding: 12px 30px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-confirm:hover {
    background: linear-gradient(45deg, #27ae60, #229954);
    transform: scale(1.05);
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 30px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3000;
    border-radius: 20px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(231, 76, 60, 0.3);
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 4000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 10px;
    padding: 15px 20px;
    color: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    border-left: 4px solid;
    animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
    max-width: 350px;
    word-wrap: break-word;
}

.notification.success {
    border-left-color: #2ecc71;
}

.notification.error {
    border-left-color: #e74c3c;
}

.notification.info {
    border-left-color: #3498db;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: