-- ========================================
-- CB Gun Shop - Constants
-- الثوابت والقيم الثابتة
-- ========================================

local Constants = {}

-- ========================================
-- ثوابت النظام
-- ========================================

Constants.SCRIPT_NAME = "CB_GunShop"
Constants.VERSION = "2.0.0"
Constants.AUTHOR = "CB Team"

-- ========================================
-- ثوابت الأحداث
-- ========================================

Constants.EVENTS = {
    -- أحداث الخادم
    SERVER = {
        OPEN_SHOP = "cb_gunshop:openShop",
        BUY_WEAPON = "cb_gunshop:buyWeapon",
        GET_SHOP_DATA = "cb_gunshop:getShopData",
        UPDATE_MONEY = "cb_gunshop:updateMoney"
    },
    
    -- أحداث العميل
    CLIENT = {
        OPEN_UI = "cb_gunshop:openUI",
        CLOSE_UI = "cb_gunshop:closeUI",
        UPDATE_UI = "cb_gunshop:updateUI",
        NOTIFICATION = "cb_gunshop:notification"
    },
    
    -- أحداث NUI
    NUI = {
        BUY_WEAPON = "buyWeapon",
        CLOSE_SHOP = "closeShop",
        SEARCH_WEAPONS = "searchWeapons",
        FILTER_CATEGORY = "filterCategory"
    }
}

-- ========================================
-- ثوابت الأخطاء
-- ========================================

Constants.ERRORS = {
    INVALID_SHOP = "ERROR_INVALID_SHOP",
    INVALID_WEAPON = "ERROR_INVALID_WEAPON",
    INVALID_PRICE = "ERROR_INVALID_PRICE",
    INVALID_AMMO = "ERROR_INVALID_AMMO",
    INSUFFICIENT_MONEY = "ERROR_INSUFFICIENT_MONEY",
    NO_PERMISSION = "ERROR_NO_PERMISSION",
    SHOP_CLOSED = "ERROR_SHOP_CLOSED",
    DAILY_LIMIT = "ERROR_DAILY_LIMIT",
    ANTICHEAT_TRIGGERED = "ERROR_ANTICHEAT"
}

-- ========================================
-- ثوابت السجلات
-- ========================================

Constants.LOG_TYPES = {
    SHOP_OPENED = "SHOP_OPENED",
    SHOP_CLOSED = "SHOP_CLOSED",
    WEAPON_PURCHASED = "WEAPON_PURCHASED",
    PURCHASE_FAILED = "PURCHASE_FAILED",
    ACCESS_DENIED = "ACCESS_DENIED",
    ANTICHEAT_PRICE = "ANTICHEAT_PRICE_MANIPULATION",
    ANTICHEAT_WEAPON = "ANTICHEAT_INVALID_WEAPON",
    ANTICHEAT_AMMO = "ANTICHEAT_INVALID_AMMO",
    ERROR_OCCURRED = "ERROR_OCCURRED"
}

-- ========================================
-- ثوابت الواجهة
-- ========================================

Constants.UI = {
    -- أنواع الإشعارات
    NOTIFICATION_TYPES = {
        SUCCESS = "success",
        ERROR = "error",
        WARNING = "warning",
        INFO = "info"
    },
    
    -- أنواع الأزرار
    BUTTON_TYPES = {
        PRIMARY = "btn-primary",
        SECONDARY = "btn-secondary",
        SUCCESS = "btn-success",
        DANGER = "btn-danger",
        WARNING = "btn-warning",
        INFO = "btn-info"
    },
    
    -- أحجام الشاشة
    BREAKPOINTS = {
        MOBILE = 768,
        TABLET = 1024,
        DESKTOP = 1200
    }
}

-- ========================================
-- ثوابت الأسلحة
-- ========================================

Constants.WEAPON = {
    -- فئات الأسلحة
    CATEGORIES = {
        PISTOL = "pistol",
        RIFLE = "rifle",
        SMG = "smg",
        SNIPER = "sniper",
        SHOTGUN = "shotgun",
        MG = "mg",
        MELEE = "melee",
        THROWABLE = "throwable"
    },
    
    -- أنواع الذخيرة
    AMMO_TYPES = {
        PISTOL = "AMMO_PISTOL",
        RIFLE = "AMMO_RIFLE",
        SMG = "AMMO_SMG",
        SNIPER = "AMMO_SNIPER",
        SHOTGUN = "AMMO_SHOTGUN",
        MG = "AMMO_MG"
    },
    
    -- حدود الإحصائيات
    STATS = {
        MIN_DAMAGE = 0,
        MAX_DAMAGE = 100,
        MIN_ACCURACY = 0,
        MAX_ACCURACY = 100,
        MIN_RANGE = 0,
        MAX_RANGE = 100,
        MIN_FIRE_RATE = 0,
        MAX_FIRE_RATE = 100
    }
}

-- ========================================
-- ثوابت المتاجر
-- ========================================

Constants.SHOP = {
    -- أنواع المتاجر
    TYPES = {
        PUBLIC = "public",
        POLICE = "police",
        VIP = "vip",
        BLACK_MARKET = "black_market"
    },
    
    -- حالات المتجر
    STATUS = {
        OPEN = "open",
        CLOSED = "closed",
        MAINTENANCE = "maintenance"
    },
    
    -- أنواع البليب
    BLIP_SPRITES = {
        GUN_SHOP = 110,
        POLICE_STATION = 60,
        VIP_AREA = 205,
        BLACK_MARKET = 378
    },
    
    -- ألوان البليب
    BLIP_COLORS = {
        WHITE = 0,
        RED = 1,
        GREEN = 2,
        BLUE = 3,
        YELLOW = 5,
        PURPLE = 6,
        ORANGE = 17
    }
}

-- ========================================
-- ثوابت الأمان
-- ========================================

Constants.SECURITY = {
    -- حدود زمنية (بالثواني)
    COOLDOWNS = {
        PURCHASE = 5,           -- 5 ثواني بين المشتريات
        SHOP_ACCESS = 2,        -- ثانيتان بين فتح المتاجر
        UI_INTERACTION = 1      -- ثانية واحدة بين تفاعلات الواجهة
    },
    
    -- حدود يومية
    DAILY_LIMITS = {
        DEFAULT_PURCHASES = 10,
        VIP_PURCHASES = 20,
        POLICE_PURCHASES = 50
    },
    
    -- مستويات التهديد
    THREAT_LEVELS = {
        LOW = 1,
        MEDIUM = 2,
        HIGH = 3,
        CRITICAL = 4
    }
}

-- ========================================
-- ثوابت الأداء
-- ========================================

Constants.PERFORMANCE = {
    -- معدلات التحديث (بالميلي ثانية)
    UPDATE_RATES = {
        FAST = 0,           -- كل إطار
        NORMAL = 100,       -- كل 100 ميلي ثانية
        SLOW = 500,         -- كل 500 ميلي ثانية
        VERY_SLOW = 1000    -- كل ثانية
    },
    
    -- مسافات التحقق
    DISTANCES = {
        INTERACTION = 2.5,
        MARKER = 8.0,
        CHECK = 15.0,
        BLIP_REFRESH = 100.0
    },
    
    -- حدود الذاكرة
    MEMORY_LIMITS = {
        MAX_LOGS = 1000,
        MAX_CACHE_SIZE = 500,
        CLEANUP_INTERVAL = 300000 -- 5 دقائق
    }
}

-- ========================================
-- ثوابت الرسائل
-- ========================================

Constants.MESSAGES = {
    -- رسائل النجاح
    SUCCESS = {
        PURCHASE_COMPLETE = "تم الشراء بنجاح",
        SHOP_OPENED = "تم فتح المتجر",
        DATA_SAVED = "تم حفظ البيانات"
    },
    
    -- رسائل الخطأ
    ERROR = {
        INSUFFICIENT_MONEY = "لا يوجد مال كافي",
        NO_PERMISSION = "ليس لديك صلاحية",
        SHOP_CLOSED = "المتجر مغلق",
        INVALID_WEAPON = "سلاح غير صالح",
        DAILY_LIMIT = "تم الوصول للحد اليومي"
    },
    
    -- رسائل التحذير
    WARNING = {
        SHOP_CLOSING = "المتجر سيغلق قريباً",
        LOW_MONEY = "أموالك قليلة",
        HIGH_PRICE = "السعر مرتفع"
    }
}

-- ========================================
-- ثوابت التكوين
-- ========================================

Constants.CONFIG = {
    -- القيم الافتراضية
    DEFAULTS = {
        AMMO_PRICE = 5,
        MAX_AMMO = 250,
        MIN_AMMO = 0,
        DEFAULT_AMMO = 30,
        DISCOUNT_PERCENTAGE = 0,
        MAX_DAILY_PURCHASES = 10
    },
    
    -- حدود التكوين
    LIMITS = {
        MAX_SHOPS = 50,
        MAX_WEAPONS_PER_SHOP = 100,
        MAX_CATEGORIES = 20,
        MAX_PERMISSIONS_PER_SHOP = 10
    }
}

-- ========================================
-- ثوابت الملفات
-- ========================================

Constants.FILES = {
    -- مسارات الملفات
    PATHS = {
        CONFIG = "new_config.lua",
        CLIENT = "new_client.lua",
        SERVER = "new_server.lua",
        UI_HTML = "new_ui/index.html",
        UI_CSS = "new_ui/style.css",
        UI_JS = "new_ui/script.js",
        IMAGES = "new_ui/img/",
        SOUNDS = "new_ui/sounds/"
    },
    
    -- أنواع الملفات المدعومة
    SUPPORTED_FORMATS = {
        IMAGES = {"png", "jpg", "jpeg", "gif"},
        SOUNDS = {"mp3", "wav", "ogg"}
    }
}

-- ========================================
-- دوال مساعدة للثوابت
-- ========================================

-- الحصول على ثابت بالمسار
function Constants.Get(path)
    local keys = {}
    for key in path:gmatch("[^%.]+") do
        table.insert(keys, key)
    end
    
    local current = Constants
    for _, key in ipairs(keys) do
        if current[key] then
            current = current[key]
        else
            return nil
        end
    end
    
    return current
end

-- التحقق من وجود ثابت
function Constants.Exists(path)
    return Constants.Get(path) ~= nil
end

-- ========================================
-- تصدير الثوابت
-- ========================================

return Constants
