<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CB Gun Shop</title>
    
    <!-- External Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11" integrity="sha256-+0Qf8IHMJWuYlZ2lQDBrF1+2aigIRZXEdSvegtELo2I=" crossorigin="anonymous"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet" media="all">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" media="all">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Main Shop Container -->
    <div id="shopContainer" class="shop-container" style="display: none;">
        <!-- Header -->
        <header class="shop-header">
            <div class="header-left">
                <img id="serverLogo" src="" alt="Server Logo" class="server-logo">
                <div class="server-info">
                    <h1 id="serverName" class="server-name">CB Store</h1>
                    <p id="shopName" class="shop-name">متجر الأسلحة</p>
                </div>
            </div>
            
            <div class="header-right">
                <div class="player-money">
                    <i class="fas fa-wallet"></i>
                    <span id="playerMoney">0</span>
                    <span class="currency">$</span>
                </div>
                <button id="closeBtn" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </header>

        <!-- Search and Filter Section -->
        <section class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="البحث عن سلاح...">
                </div>
                
                <div class="filter-container">
                    <button class="filter-btn active" data-category="all">
                        <i class="fas fa-th"></i>
                        الكل
                    </button>
                </div>
            </div>
        </section>

        <!-- Weapons Grid -->
        <main class="weapons-section">
            <div id="weaponsGrid" class="weapons-grid">
                <!-- Weapons will be dynamically loaded here -->
            </div>
        </main>

        <!-- Purchase Modal -->
        <div id="purchaseModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>تأكيد الشراء</h2>
                    <button class="modal-close">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="weapon-preview">
                        <img id="modalWeaponImage" src="" alt="Weapon">
                        <div class="weapon-details">
                            <h3 id="modalWeaponName">اسم السلاح</h3>
                            <p id="modalWeaponDescription">وصف السلاح</p>
                        </div>
                    </div>
                    
                    <div class="weapon-stats">
                        <div class="stat">
                            <span class="stat-label">الضرر</span>
                            <div class="stat-bar">
                                <div id="damageStat" class="stat-fill"></div>
                            </div>
                            <span id="damageValue" class="stat-value">0</span>
                        </div>
                        
                        <div class="stat">
                            <span class="stat-label">الدقة</span>
                            <div class="stat-bar">
                                <div id="accuracyStat" class="stat-fill"></div>
                            </div>
                            <span id="accuracyValue" class="stat-value">0</span>
                        </div>
                        
                        <div class="stat">
                            <span class="stat-label">المدى</span>
                            <div class="stat-bar">
                                <div id="rangeStat" class="stat-fill"></div>
                            </div>
                            <span id="rangeValue" class="stat-value">0</span>
                        </div>
                        
                        <div class="stat">
                            <span class="stat-label">معدل الإطلاق</span>
                            <div class="stat-bar">
                                <div id="fireRateStat" class="stat-fill"></div>
                            </div>
                            <span id="fireRateValue" class="stat-value">0</span>
                        </div>
                    </div>
                    
                    <div class="ammo-selection">
                        <label for="ammoSlider">كمية الذخيرة:</label>
                        <div class="slider-container">
                            <input type="range" id="ammoSlider" min="0" max="250" value="30" class="ammo-slider">
                            <div class="slider-labels">
                                <span>0</span>
                                <span id="ammoValue">30</span>
                                <span>250</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="price-breakdown">
                        <div class="price-row">
                            <span>سعر السلاح:</span>
                            <span id="weaponPrice">0$</span>
                        </div>
                        <div class="price-row">
                            <span>سعر الذخيرة:</span>
                            <span id="ammoPrice">0$</span>
                        </div>
                        <div class="price-row total">
                            <span>المجموع:</span>
                            <span id="totalPrice">0$</span>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button id="confirmPurchase" class="btn btn-primary">
                        <i class="fas fa-shopping-cart"></i>
                        تأكيد الشراء
                    </button>
                    <button id="cancelPurchase" class="btn btn-secondary">
                        إلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
