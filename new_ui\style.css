/* ========================================
   CB Gun Shop - Modern UI Styles
   ======================================== */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance Optimizations */
*,
*::before,
*::after {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* GPU Acceleration for animations */
.weapon-card,
.btn,
.modal,
.notification {
    will-change: transform;
    transform: translateZ(0);
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    direction: rtl;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Main Container */
.shop-container {
    width: 95vw;
    height: 90vh;
    max-width: 1400px;
    margin: 5vh auto;
    background: rgba(26, 26, 46, 0.95);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(90deg, rgba(0, 212, 255, 0.1), rgba(0, 150, 255, 0.05));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.server-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid rgba(0, 212, 255, 0.5);
    object-fit: cover;
}

.server-name {
    font-size: 24px;
    font-weight: 700;
    color: #00d4ff;
    margin-bottom: 5px;
}

.shop-name {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.player-money {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(45deg, #00d4ff, #0096ff);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
}

.close-btn {
    background: linear-gradient(45deg, #ff4757, #ff3742);
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
}

/* Search Section */
.search-section {
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: white;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
}

.search-box input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.filter-container {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(45deg, #00d4ff, #0096ff);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Weapons Section */
.weapons-section {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
}

.weapons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding-bottom: 20px;
}

/* Weapon Card */
.weapon-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.weapon-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00d4ff, #0096ff);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.weapon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(0, 212, 255, 0.3);
}

.weapon-card:hover::before {
    transform: scaleX(1);
}

.weapon-image {
    width: 100%;
    height: 120px;
    object-fit: contain;
    margin-bottom: 15px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.weapon-info {
    text-align: center;
}

.weapon-name {
    font-size: 18px;
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 8px;
}

.weapon-description {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 15px;
    line-height: 1.4;
}

.weapon-price {
    font-size: 20px;
    font-weight: 700;
    color: #00ff88;
    margin-bottom: 15px;
}

.weapon-stats-mini {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 11px;
}

.stat-mini {
    text-align: center;
}

.stat-mini .label {
    color: rgba(255, 255, 255, 0.6);
    display: block;
    margin-bottom: 2px;
}

.stat-mini .value {
    color: #00d4ff;
    font-weight: 600;
}

.buy-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    border: none;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.buy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    backdrop-filter: blur(20px);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: #00d4ff;
    font-size: 20px;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
}

.modal-body {
    padding: 30px;
}

.weapon-preview {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;
}

.weapon-preview img {
    width: 120px;
    height: 80px;
    object-fit: contain;
}

.weapon-details h3 {
    color: #00d4ff;
    font-size: 18px;
    margin-bottom: 8px;
}

.weapon-details p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.4;
}

/* Weapon Stats */
.weapon-stats {
    margin-bottom: 30px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.stat-label {
    min-width: 80px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.stat-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #0096ff);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.stat-value {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: #00d4ff;
    font-size: 12px;
}

/* Ammo Selection */
.ammo-selection {
    margin-bottom: 30px;
}

.ammo-selection label {
    display: block;
    margin-bottom: 15px;
    color: #00d4ff;
    font-weight: 600;
}

.slider-container {
    position: relative;
}

.ammo-slider {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    outline: none;
    -webkit-appearance: none;
}

.ammo-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #00d4ff, #0096ff);
    border-radius: 50%;
    cursor: pointer;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.slider-labels #ammoValue {
    color: #00d4ff;
    font-weight: 600;
}

/* Price Breakdown */
.price-breakdown {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.price-row.total {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 10px;
    margin-top: 10px;
    font-weight: 600;
    font-size: 16px;
    color: #00ff88;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
}

.notification {
    background: linear-gradient(45deg, #00d4ff, #0096ff);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.notification.error {
    background: linear-gradient(45deg, #ff4757, #ff3742);
}

.notification.success {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00d4ff, #0096ff);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #0096ff, #007acc);
}

/* Responsive Design */

/* Mobile First Approach */
@media (max-width: 480px) {
    .shop-container {
        width: 100vw;
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .shop-header {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;
    }

    .server-logo {
        width: 40px;
        height: 40px;
    }

    .server-name {
        font-size: 18px;
    }

    .player-money {
        padding: 8px 15px;
        font-size: 14px;
    }

    .search-box input {
        padding: 10px 40px 10px 12px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .weapons-grid {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 10px;
    }

    .weapon-card {
        padding: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-body {
        padding: 20px;
    }

    .weapon-preview {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .weapon-preview img {
        width: 80px;
        height: 60px;
    }
}

/* Tablet */
@media (min-width: 481px) and (max-width: 768px) {
    .shop-container {
        width: 95vw;
        height: 90vh;
        margin: 5vh auto;
    }

    .shop-header {
        padding: 15px 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .search-container {
        flex-direction: column;
        gap: 15px;
    }

    .search-box {
        min-width: auto;
    }

    .weapons-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }
}

/* Desktop */
@media (min-width: 769px) and (max-width: 1024px) {
    .weapons-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Desktop */
@media (min-width: 1025px) {
    .weapons-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .weapon-image,
    .server-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .shop-container {
        height: 95vh;
    }

    .shop-header {
        padding: 10px 20px;
    }

    .weapons-section {
        padding: 10px 20px;
    }
}

/* Print Styles (Hidden) */
@media print {
    .shop-container {
        display: none !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast */
@media (prefers-contrast: high) {
    .weapon-card {
        border: 2px solid #ffffff;
    }

    .btn {
        border: 2px solid currentColor;
    }
}
