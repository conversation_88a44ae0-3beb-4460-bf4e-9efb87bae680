-- Proxy interface system
Proxy = {}

local proxy_rdata = {}
local function proxy_callback(rvalues)
    proxy_rdata = rvalues
end

local function proxy_resolve(itable,key)
    local iname = getmetatable(itable).name
    local ids = getmetatable(itable).ids
    local callbacks = getmetatable(itable).callbacks
    local identifier = ids:gen()
    local fcall = function(args,callback)
        if args == nil then
            args = {}
        end
        TriggerEvent(iname..":proxy",identifier,key,args,callback or proxy_callback)
    end
    itable[key] = fcall
    return fcall
end

function Proxy.addInterface(name, itable)
    AddEventHandler(name..":proxy",function(identifier,key,args,callback)
        local res = itable[key](table.unpack(args,1,table.maxn(args)))
        callback({res})
    end)
end

function Proxy.getInterface(name)
    local ids = Tools.newIDGenerator()
    local callbacks = {}
    local r = setmetatable({},{__index=proxy_resolve, name=name, ids=ids, callbacks=callbacks})
    return r
end

-- Tools
Tools = {}

function Tools.newIDGenerator()
    local r = setmetatable({},{__index = Tools})
    r.max = 0
    r.ids = {}
    return r
end

function Tools:gen()
    if #self.ids > 0 then
        return table.remove(self.ids)
    else
        local r = self.max
        self.max = self.max+1
        return r
    end
end

function Tools:free(id)
    table.insert(self.ids,id)
end