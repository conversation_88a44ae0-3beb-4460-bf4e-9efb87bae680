-- ========================================
-- CB Gun Shop - Server Script
-- سكربت الخادم لمتجر الأسلحة
-- ========================================

-- تحميل إطار العمل
local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")
vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP", "cb_gunshop")

-- تحميل التكوين والأنظمة المساعدة
Config = Config or {} -- تأكد من وجود Config
local ErrorHandler = require('lib/error_handler')
local Logger = require('lib/logger')
local Utils = require('lib/utils')
local Constants = require('lib/constants')
local Security = require('lib/security')

-- تهيئة الأنظمة
ErrorHandler.Initialize(Config.Debug)
Logger.Initialize()
Security.Initialize()

-- متغيرات عامة
local playerPurchases = {} -- تتبع مشتريات اللاعبين
local playerCooldowns = {} -- فترات الانتظار
local shopLogs = {} -- سجلات المتجر

-- ========================================
-- دوال مساعدة
-- ========================================

-- تسجيل الأحداث (محدث لاستخدام النظام الجديد)
local function LogEvent(eventType, playerId, data)
    if not Config.Security.LogTransactions then return end

    -- استخدام نظام السجلات الجديد
    if eventType:find("PURCHASE") then
        Logger.LogTransaction(playerId, data.weapon or "unknown", data.price or 0, data.ammo or 0, data.shopId or "unknown", eventType == "WEAPON_PURCHASED")
    elseif eventType:find("ACCESS") or eventType:find("SHOP") then
        Logger.LogShopAccess(playerId, data.shopId or "unknown", eventType:lower(), true)
    elseif eventType:find("ANTICHEAT") or eventType:find("ERROR") then
        Logger.LogSecurityEvent(playerId, eventType, "high", data)
    else
        Logger.LogUserAction(playerId, eventType, data.target or "shop", data.result or "unknown")
    end

    -- إضافة للسجل القديم للتوافق
    local logEntry = {
        timestamp = os.date("%Y-%m-%d %H:%M:%S"),
        type = eventType,
        playerId = playerId,
        data = data
    }

    table.insert(shopLogs, logEntry)

    -- طباعة في وحدة التحكم إذا كان وضع التطوير مفعل
    if Config.Debug then
        print(string.format("[CB_GunShop] %s - Player %s: %s", logEntry.timestamp, playerId, eventType))
    end
end

-- التحقق من صلاحيات اللاعب
local function GetPlayerPermissions(userId)
    local permissions = {}
    
    -- التحقق من الصلاحيات الأساسية
    if vRP.hasPermission({userId, "citizen.basic"}) then
        table.insert(permissions, "citizen.basic")
    end
    
    if vRP.hasPermission({userId, "gunshop.access"}) then
        table.insert(permissions, "gunshop.access")
    end
    
    if vRP.hasPermission({userId, "police.officer"}) then
        table.insert(permissions, "police.officer")
        table.insert(permissions, "police.armory")
    end
    
    if vRP.hasPermission({userId, "vip.premium"}) then
        table.insert(permissions, "vip.premium")
    end
    
    if vRP.hasPermission({userId, "vip.gold"}) then
        table.insert(permissions, "vip.gold")
    end
    
    if vRP.hasPermission({userId, "blackmarket.access"}) then
        table.insert(permissions, "blackmarket.access")
    end
    
    if vRP.hasPermission({userId, "criminal.advanced"}) then
        table.insert(permissions, "criminal.advanced")
    end
    
    return permissions
end

-- التحقق من حدود الشراء اليومية
local function CheckDailyLimit(userId, shopId)
    local today = os.date("%Y-%m-%d")
    local key = userId .. "_" .. shopId .. "_" .. today
    
    if not playerPurchases[key] then
        playerPurchases[key] = 0
    end
    
    local shop = nil
    for _, s in ipairs(Config.Shops) do
        if s.id == shopId then
            shop = s
            break
        end
    end
    
    if not shop then return false end
    
    return playerPurchases[key] < shop.settings.maxPurchasePerDay
end

-- زيادة عداد الشراء اليومي
local function IncrementDailyPurchase(userId, shopId)
    local today = os.date("%Y-%m-%d")
    local key = userId .. "_" .. shopId .. "_" .. today
    
    if not playerPurchases[key] then
        playerPurchases[key] = 0
    end
    
    playerPurchases[key] = playerPurchases[key] + 1
end

-- التحقق من ساعات العمل
local function IsShopOpen(shopId)
    local shop = nil
    for _, s in ipairs(Config.Shops) do
        if s.id == shopId then
            shop = s
            break
        end
    end
    
    if not shop then return false end
    
    local currentHour = tonumber(os.date("%H"))
    local startHour = shop.settings.openHours.startHour
    local endHour = shop.settings.openHours.endHour
    
    -- إذا كان المتجر مفتوح 24/7
    if startHour == 0 and endHour == 24 then
        return true
    end
    
    -- إذا كانت ساعات العمل عادية (مثل 6-22)
    if startHour < endHour then
        return currentHour >= startHour and currentHour < endHour
    end
    
    -- إذا كانت ساعات العمل تمتد لليوم التالي (مثل 22-6)
    return currentHour >= startHour or currentHour < endHour
end

-- حساب السعر النهائي مع الخصومات
local function CalculateFinalPrice(basePrice, shopId, userId)
    local shop = nil
    for _, s in ipairs(Config.Shops) do
        if s.id == shopId then
            shop = s
            break
        end
    end
    
    if not shop then return basePrice end
    
    local discount = shop.settings.discountPercentage or 0
    local finalPrice = basePrice
    
    if discount > 0 then
        finalPrice = basePrice * (1 - discount / 100)
    elseif discount < 0 then
        finalPrice = basePrice * (1 + math.abs(discount) / 100)
    end
    
    return math.floor(finalPrice)
end

-- التحقق من مكافحة الغش
local function AntiCheatCheck(userId, weaponName, price, ammo)
    if not Config.Security.EnableAntiCheat then return true end
    
    -- التحقق من وجود السلاح في قاعدة البيانات
    local weaponExists = false
    local correctPrice = 0
    
    for _, weapon in ipairs(Config.WeaponDatabase) do
        if weapon.name == weaponName then
            weaponExists = true
            correctPrice = weapon.price
            break
        end
    end
    
    if not weaponExists then
        LogEvent("ANTICHEAT_INVALID_WEAPON", userId, {weapon = weaponName})
        return false
    end
    
    -- التحقق من السعر
    if math.abs(price - correctPrice) > correctPrice * 0.1 then -- هامش خطأ 10%
        LogEvent("ANTICHEAT_PRICE_MANIPULATION", userId, {
            weapon = weaponName,
            sentPrice = price,
            correctPrice = correctPrice
        })
        return false
    end
    
    -- التحقق من كمية الذخيرة
    if ammo and (ammo < Config.Weapons.MinAmmo or ammo > Config.Weapons.MaxAmmo) then
        LogEvent("ANTICHEAT_INVALID_AMMO", userId, {
            weapon = weaponName,
            ammo = ammo,
            maxAllowed = Config.Weapons.MaxAmmo
        })
        return false
    end
    
    return true
end

-- ========================================
-- الأحداث الشبكية
-- ========================================

-- فتح المتجر
RegisterServerEvent("cb_gunshop:openShop")
AddEventHandler("cb_gunshop:openShop", function(shopData)
    local source = source
    local userId = vRP.getUserId({source})

    -- التحقق من صحة البيانات
    if not userId then
        ErrorHandler.Warning("Invalid user ID in openShop", {source = source}, "ShopAccess")
        return
    end

    if not shopData or type(shopData) ~= "table" then
        ErrorHandler.Error("Invalid shop data received", {userId = userId, shopData = shopData}, "ShopAccess")
        return
    end

    -- فحص الأمان الشامل
    local securityPassed, violations = Security.AntiCheatCheck(userId, "shop_access", shopData)
    if not securityPassed then
        ErrorHandler.Warning("Security check failed for shop access", {
            userId = userId,
            violations = violations,
            shopId = shopData.id
        }, "Security")

        vRPclient.notify(source, {"تم رفض الوصول لأسباب أمنية"})
        return
    end

    -- استخدام بيانات المتجر المرسلة من العميل
    local shop = shopData

    if not shop or not shop.id then
        LogEvent("ERROR_INVALID_SHOP", userId, {shopData = shopData})
        return
    end
    
    -- التحقق من الصلاحيات
    local playerPermissions = GetPlayerPermissions(userId)
    if not Config.CanAccessShop(shop.id, playerPermissions) then
        vRPclient.notify(source, {Config.GetMessage("no_permission")})
        LogEvent("ACCESS_DENIED", userId, {shopId = shop.id})
        return
    end
    
    -- التحقق من ساعات العمل
    if not IsShopOpen(shop.id) then
        vRPclient.notify(source, {Config.GetMessage("shop_closed_hours")})
        return
    end

    -- الحصول على أموال اللاعب
    local playerMoney = vRP.getMoney({userId}) + vRP.getBankMoney({userId})

    -- الحصول على أسلحة المتجر (استخدام الأسلحة المرسلة من العميل)
    local shopWeapons = shop.weapons or Config.GetShopWeapons(shop.id)
    
    -- إرسال البيانات للعميل
    TriggerClientEvent("cb_gunshop:openUI", source, {
        shop = shop,
        weapons = shopWeapons,
        playerMoney = playerMoney,
        serverInfo = {
            name = Config.UI.ServerName,
            logo = Config.UI.ServerLogo
        }
    })
    
    LogEvent("SHOP_OPENED", userId, {shopId = shop.id})
end)

-- شراء سلاح
RegisterServerEvent("cb_gunshop:buyWeapon")
AddEventHandler("cb_gunshop:buyWeapon", function(data)
    local source = source
    local userId = vRP.getUserId({source})

    -- التحقق من صحة البيانات الأساسية
    if not userId then
        ErrorHandler.Warning("Invalid user ID in buyWeapon", {source = source}, "WeaponPurchase")
        return
    end

    if not data or type(data) ~= "table" then
        ErrorHandler.Error("Invalid purchase data", {userId = userId, data = data}, "WeaponPurchase")
        return
    end

    -- التحقق من وجود البيانات المطلوبة
    if not data.weapon or not data.price or not data.shopId then
        ErrorHandler.Error("Missing required purchase data", {
            userId = userId,
            weapon = data.weapon,
            price = data.price,
            shopId = data.shopId
        }, "WeaponPurchase")
        return
    end
    
    local weaponName = data.weapon
    local shopId = data.shopId
    local ammoCount = data.ammo or Config.Weapons.DefaultAmmo

    -- فحص الأمان الشامل للشراء
    local securityPassed, violations = Security.AntiCheatCheck(userId, "purchase", data)
    if not securityPassed then
        ErrorHandler.Warning("Security check failed for weapon purchase", {
            userId = userId,
            violations = violations,
            weapon = weaponName,
            shopId = shopId
        }, "Security")

        vRPclient.notify(source, {"تم رفض الشراء لأسباب أمنية"})
        LogEvent("PURCHASE_BLOCKED_SECURITY", userId, {
            weapon = weaponName,
            violations = violations,
            shopId = shopId
        })
        return
    end
    
    -- التحقق من الحدود اليومية
    if not CheckDailyLimit(userId, shopId) then
        vRPclient.notify(source, {Config.GetMessage("daily_limit_reached")})
        return
    end
    
    -- حساب السعر النهائي
    local basePrice = data.price + (ammoCount * Config.Weapons.AmmoPrice)
    local finalPrice = CalculateFinalPrice(basePrice, shopId, userId)
    
    -- التحقق من الأموال
    if not vRP.tryFullPayment({userId, finalPrice}) then
        vRPclient.notify(source, {Config.GetMessage("insufficient_money")})
        return
    end
    
    -- إعطاء السلاح للاعب
    local weaponData = {}
    weaponData[weaponName] = {ammo = ammoCount}
    
    vRPclient.giveWeapons(source, {weaponData}, false)
    
    -- تحديث العدادات
    IncrementDailyPurchase(userId, shopId)
    
    -- إرسال رسالة نجاح
    vRPclient.notify(source, {Config.GetMessage("purchase_success")})
    
    -- تسجيل المعاملة
    LogEvent("WEAPON_PURCHASED", userId, {
        weapon = weaponName,
        ammo = ammoCount,
        price = finalPrice,
        shopId = shopId
    })
end)
