-- ========================================
-- CB Gun Shop - Advanced Logger
-- نظام السجلات المتقدم
-- ========================================

local Logger = {}
local Utils = require('lib/utils')
local Constants = require('lib/constants')

-- ========================================
-- إعدادات السجلات
-- ========================================

local LOG_CONFIG = {
    MAX_ENTRIES = 5000,                 -- أقصى عدد إدخالات
    RETENTION_DAYS = 7,                 -- مدة الاحتفاظ بالسجلات
    AUTO_SAVE_INTERVAL = 300,           -- حفظ تلقائي كل 5 دقائق
    BATCH_SIZE = 100,                   -- حجم الدفعة للحفظ
    COMPRESSION_ENABLED = true,         -- ضغط السجلات القديمة
    ENCRYPTION_ENABLED = false          -- تشفير السجلات الحساسة
}

-- ========================================
-- أنواع السجلات
-- ========================================

local LOG_TYPES = {
    TRANSACTION = "transaction",        -- معاملات الشراء
    ACCESS = "access",                  -- الوصول للمتاجر
    SECURITY = "security",              -- أحداث الأمان
    PERFORMANCE = "performance",        -- أداء النظام
    USER_ACTION = "user_action",        -- أفعال المستخدمين
    SYSTEM = "system",                  -- أحداث النظام
    DEBUG = "debug"                     -- معلومات التطوير
}

-- ========================================
-- متغيرات النظام
-- ========================================

local logs = {}
local logStats = {}
local isInitialized = false
local saveQueue = {}

-- ========================================
-- دوال التسجيل الأساسية
-- ========================================

-- تسجيل حدث جديد
function Logger.Log(logType, event, data, userId, metadata)
    if not isInitialized then
        Logger.Initialize()
    end
    
    local timestamp = os.time()
    local logEntry = {
        id = Utils.GenerateUniqueId(),
        type = logType,
        event = event,
        data = data or {},
        userId = userId,
        metadata = metadata or {},
        timestamp = timestamp,
        formattedTime = Utils.FormatTime(timestamp),
        serverTime = os.date("%Y-%m-%d %H:%M:%S", timestamp),
        resourceName = GetCurrentResourceName()
    }
    
    -- إضافة معلومات إضافية
    logEntry.metadata.serverUptime = GetGameTimer()
    logEntry.metadata.playerCount = GetNumPlayerIndices()
    
    -- تسجيل الحدث
    table.insert(logs, logEntry)
    
    -- تحديث الإحصائيات
    Logger.UpdateStats(logType, event)
    
    -- طباعة في وضع التطوير
    if Config.Debug then
        Logger.PrintLog(logEntry)
    end
    
    -- إضافة للطابور للحفظ
    table.insert(saveQueue, logEntry)
    
    -- تنظيف السجلات القديمة
    Logger.CleanupOldLogs()
    
    return logEntry.id
end

-- طباعة السجل
function Logger.PrintLog(logEntry)
    local message = string.format(
        "[%s] [%s] %s: %s",
        Constants.SCRIPT_NAME,
        logEntry.type:upper(),
        logEntry.event,
        logEntry.userId and ("User " .. logEntry.userId) or "System"
    )
    
    print("^2" .. message .. "^7")
    
    if logEntry.data and next(logEntry.data) then
        print("^5[DATA] " .. json.encode(logEntry.data) .. "^7")
    end
end

-- تحديث الإحصائيات
function Logger.UpdateStats(logType, event)
    if not logStats[logType] then
        logStats[logType] = {
            total = 0,
            events = {},
            lastActivity = 0
        }
    end
    
    logStats[logType].total = logStats[logType].total + 1
    logStats[logType].events[event] = (logStats[logType].events[event] or 0) + 1
    logStats[logType].lastActivity = os.time()
end

-- ========================================
-- دوال التسجيل المتخصصة
-- ========================================

-- تسجيل معاملة شراء
function Logger.LogTransaction(userId, weaponName, price, ammo, shopId, success)
    local data = {
        weapon = weaponName,
        price = price,
        ammo = ammo,
        shopId = shopId,
        success = success,
        totalCost = price + (ammo * Config.Weapons.AmmoPrice)
    }
    
    local event = success and "purchase_success" or "purchase_failed"
    return Logger.Log(LOG_TYPES.TRANSACTION, event, data, userId)
end

-- تسجيل الوصول للمتجر
function Logger.LogShopAccess(userId, shopId, action, success)
    local data = {
        shopId = shopId,
        action = action, -- "open", "close", "denied"
        success = success
    }
    
    return Logger.Log(LOG_TYPES.ACCESS, "shop_" .. action, data, userId)
end

-- تسجيل حدث أمني
function Logger.LogSecurityEvent(userId, eventType, severity, details)
    local data = {
        eventType = eventType,
        severity = severity, -- "low", "medium", "high", "critical"
        details = details,
        ipAddress = GetPlayerEndpoint(userId) or "unknown"
    }
    
    return Logger.Log(LOG_TYPES.SECURITY, eventType, data, userId)
end

-- تسجيل أداء النظام
function Logger.LogPerformance(metric, value, context)
    local data = {
        metric = metric,
        value = value,
        context = context,
        memoryUsage = collectgarbage("count"),
        playerCount = GetNumPlayerIndices()
    }
    
    return Logger.Log(LOG_TYPES.PERFORMANCE, "metric_recorded", data)
end

-- تسجيل فعل المستخدم
function Logger.LogUserAction(userId, action, target, result)
    local data = {
        action = action,
        target = target,
        result = result,
        timestamp = os.time()
    }
    
    return Logger.Log(LOG_TYPES.USER_ACTION, action, data, userId)
end

-- ========================================
-- دوال الاستعلام والبحث
-- ========================================

-- البحث في السجلات
function Logger.SearchLogs(criteria)
    local results = {}
    
    for _, log in ipairs(logs) do
        local match = true
        
        -- البحث حسب النوع
        if criteria.type and log.type ~= criteria.type then
            match = false
        end
        
        -- البحث حسب المستخدم
        if criteria.userId and log.userId ~= criteria.userId then
            match = false
        end
        
        -- البحث حسب الفترة الزمنية
        if criteria.startTime and log.timestamp < criteria.startTime then
            match = false
        end
        
        if criteria.endTime and log.timestamp > criteria.endTime then
            match = false
        end
        
        -- البحث في النص
        if criteria.searchText then
            local searchText = criteria.searchText:lower()
            local found = false
            
            if log.event:lower():find(searchText) then
                found = true
            elseif json.encode(log.data):lower():find(searchText) then
                found = true
            end
            
            if not found then
                match = false
            end
        end
        
        if match then
            table.insert(results, log)
        end
    end
    
    return results
end

-- الحصول على سجلات حديثة
function Logger.GetRecentLogs(count, logType)
    local filtered = {}
    
    for i = #logs, 1, -1 do
        local log = logs[i]
        
        if not logType or log.type == logType then
            table.insert(filtered, log)
            
            if #filtered >= (count or 100) then
                break
            end
        end
    end
    
    return filtered
end

-- الحصول على إحصائيات مفصلة
function Logger.GetDetailedStats()
    local stats = {
        totalLogs = #logs,
        logsByType = {},
        recentActivity = {},
        topUsers = {},
        systemHealth = {}
    }
    
    -- إحصائيات حسب النوع
    for logType, typeStats in pairs(logStats) do
        stats.logsByType[logType] = {
            total = typeStats.total,
            events = typeStats.events,
            lastActivity = typeStats.lastActivity
        }
    end
    
    -- النشاط الحديث (آخر ساعة)
    local oneHourAgo = os.time() - 3600
    local recentCount = 0
    
    for _, log in ipairs(logs) do
        if log.timestamp > oneHourAgo then
            recentCount = recentCount + 1
        end
    end
    
    stats.recentActivity.count = recentCount
    stats.recentActivity.rate = recentCount / 60 -- أحداث في الدقيقة
    
    -- صحة النظام
    stats.systemHealth.memoryUsage = collectgarbage("count")
    stats.systemHealth.logQueueSize = #saveQueue
    stats.systemHealth.uptime = GetGameTimer()
    
    return stats
end

-- ========================================
-- دوال التصدير والحفظ
-- ========================================

-- تصدير السجلات
function Logger.ExportLogs(format, criteria)
    local logsToExport = criteria and Logger.SearchLogs(criteria) or logs
    
    if format == "json" then
        return json.encode(logsToExport)
    elseif format == "csv" then
        return Logger.ConvertToCSV(logsToExport)
    else
        return logsToExport
    end
end

-- تحويل إلى CSV
function Logger.ConvertToCSV(logData)
    local csv = "ID,Type,Event,UserID,Timestamp,Data\n"
    
    for _, log in ipairs(logData) do
        local row = string.format(
            "%s,%s,%s,%s,%s,\"%s\"\n",
            log.id,
            log.type,
            log.event,
            log.userId or "",
            log.formattedTime,
            json.encode(log.data):gsub('"', '""')
        )
        csv = csv .. row
    end
    
    return csv
end

-- ========================================
-- دوال التنظيف والصيانة
-- ========================================

-- تنظيف السجلات القديمة
function Logger.CleanupOldLogs()
    if #logs <= LOG_CONFIG.MAX_ENTRIES then
        return
    end
    
    local cutoffTime = os.time() - (LOG_CONFIG.RETENTION_DAYS * 24 * 3600)
    local newLogs = {}
    local removedCount = 0
    
    for _, log in ipairs(logs) do
        if log.timestamp > cutoffTime and #newLogs < LOG_CONFIG.MAX_ENTRIES then
            table.insert(newLogs, log)
        else
            removedCount = removedCount + 1
        end
    end
    
    logs = newLogs
    
    if removedCount > 0 and Config.Debug then
        print(string.format("^3[Logger] Cleaned up %d old log entries^7", removedCount))
    end
end

-- مسح جميع السجلات
function Logger.ClearAllLogs()
    logs = {}
    logStats = {}
    saveQueue = {}
    print("^3[Logger] All logs have been cleared^7")
end

-- ========================================
-- تهيئة النظام
-- ========================================

-- تهيئة نظام السجلات
function Logger.Initialize()
    if isInitialized then
        return
    end
    
    isInitialized = true
    
    -- تنظيف دوري
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(LOG_CONFIG.AUTO_SAVE_INTERVAL * 1000)
            Logger.CleanupOldLogs()
        end
    end)
    
    Logger.Log(LOG_TYPES.SYSTEM, "logger_initialized", {
        maxEntries = LOG_CONFIG.MAX_ENTRIES,
        retentionDays = LOG_CONFIG.RETENTION_DAYS
    })
end

-- ========================================
-- تصدير النظام
-- ========================================

return Logger
