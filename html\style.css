* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    color: white;
    direction: rtl;
}

.hidden {
    display: none !important;
}

#container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
}

.shop-container {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 15px;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e74c3c;
}

.header h1 {
    color: #e74c3c;
    font-size: 24px;
}

.money {
    font-size: 18px;
    font-weight: bold;
    color: #2ecc71;
}

.close-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.close-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.categories {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.category-btn {
    background: #34495e;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.category-btn:hover,
.category-btn.active {
    background: #e74c3c;
    transform: translateY(-2px);
}

.weapons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.weapon-card {
    background: #34495e;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s;
    border: 2px solid transparent;
}

.weapon-card:hover {
    transform: translateY(-5px);
    border-color: #e74c3c;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.weapon-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #ecf0f1;
}

.weapon-price {
    font-size: 16px;
    color: #2ecc71;
    margin-bottom: 15px;
}

.ammo-section {
    margin-bottom: 15px;
}

.ammo-section label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.ammo-input {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 5px;
    background: #2c3e50;
    color: white;
    text-align: center;
}

.buy-btn {
    background: #2ecc71;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    width: 100%;
    transition: all 0.3s;
}

.buy-btn:hover {
    background: #27ae60;
    transform: scale(1.05);
}

.buy-btn:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    transform: none;
}

/* رسائل التنبيه */
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.alert.success {
    background: #2ecc71;
}

.alert.error {
    background: #e74c3c;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}