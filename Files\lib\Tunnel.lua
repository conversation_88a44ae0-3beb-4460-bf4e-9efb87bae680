-- Tunnel interface system
Tunnel = {}

local function tunnel_resolve(itable,key)
    local mtable = getmetatable(itable)
    local iname = mtable.name
    local ids = mtable.ids
    local callbacks = mtable.callbacks
    local identifier = ids:gen()
    local fname = key
    local no_wait = false
    if string.sub(key,1,1) == "_" then
        fname = string.sub(key,2)
        no_wait = true
    end
    local fcall = function(...)
        local args = {...}
        local callback = nil
        if not no_wait then
            callback = function(rets)
                coroutine.resume(coroutine.running(), table.unpack(rets,1,table.maxn(rets)))
            end
        end
        TriggerEvent(iname..":tunnel_req",identifier,fname,args,callback)
        if not no_wait then
            return coroutine.yield()
        end
    end
    itable[key] = fcall
    return fcall
end

function Tunnel.bindInterface(name,itable)
    AddEventHandler(name..":tunnel_req",function(identifier,fname,args,callback)
        local f = itable[fname]
        if type(f) == "function" then
            local rets = {f(table.unpack(args,1,table.maxn(args)))}
            if callback ~= nil then
                callback(rets)
            end
        end
    end)
end

function Tunnel.getInterface(name,identifier)
    local ids = Tools.newIDGenerator()
    local callbacks = {}
    local r = setmetatable({},{__index=tunnel_resolve, name=name, ids=ids, callbacks=callbacks})
    return r
end