-- ========================================
-- CB Gun Shop - Client Script
-- سكربت العميل لمتجر الأسلحة
-- ========================================

-- متغيرات محلية
local isUIOpen = false
local currentShop = nil
local nearbyShops = {}
local shopBlips = {}
local shopNPCs = {}

-- تحميل الخط
local fontLoaded = false
local fontId = nil

-- ========================================
-- دوال مساعدة
-- ========================================

-- تحميل الخط
local function LoadFont()
    if not fontLoaded then
        RegisterFontFile(Config.UI.MarkerFont)
        fontId = RegisterFontId(Config.UI.MarkerFont)
        fontLoaded = true
    end
end

-- رسم النص ثلاثي الأبعاد
local function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local scale = 0.35
    
    if onScreen then
        SetTextScale(scale, scale)
        SetTextFont(fontId or 4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
        
        -- إضافة خلفية للنص
        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 68)
    end
end

-- إنشاء بليب للمتجر
local function CreateShopBlip(shop)
    local blip = AddBlipForCoord(shop.coords.x, shop.coords.y, shop.coords.z)
    SetBlipSprite(blip, shop.blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, shop.blip.scale)
    SetBlipColour(blip, shop.blip.color)
    SetBlipAsShortRange(blip, shop.blip.shortRange)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(shop.name)
    EndTextCommandSetBlipName(blip)
    
    return blip
end

-- إنشاء NPC للمتجر
local function CreateShopNPC(shop)
    if not shop.settings.npcModel then return nil end
    
    local hash = GetHashKey(shop.settings.npcModel)
    
    RequestModel(hash)
    while not HasModelLoaded(hash) do
        Wait(1)
    end
    
    local npc = CreatePed(4, hash, shop.coords.x, shop.coords.y, shop.coords.z - 1.0, shop.heading, false, true)
    
    SetEntityHeading(npc, shop.heading)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)
    
    return npc
end

-- التحقق من المسافة للمتاجر
local function CheckShopDistances()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    
    nearbyShops = {}
    currentShop = nil
    
    for _, shop in ipairs(Config.Shops) do
        local distance = GetDistanceBetweenCoords(playerCoords, shop.coords.x, shop.coords.y, shop.coords.z, true)
        
        if distance <= Config.Performance.CheckDistance then
            table.insert(nearbyShops, {
                shop = shop,
                distance = distance
            })
            
            if distance <= Config.Performance.InteractionDistance then
                currentShop = shop
            end
        end
    end
    
    -- ترتيب المتاجر حسب المسافة
    table.sort(nearbyShops, function(a, b)
        return a.distance < b.distance
    end)
end

-- ========================================
-- الأحداث الشبكية
-- ========================================

-- فتح واجهة المتجر
RegisterNetEvent("cb_gunshop:openUI")
AddEventHandler("cb_gunshop:openUI", function(data)
    if isUIOpen then return end
    
    isUIOpen = true
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        action = "openShop",
        data = data
    })
end)

-- إغلاق واجهة المتجر
RegisterNetEvent("cb_gunshop:closeUI")
AddEventHandler("cb_gunshop:closeUI", function()
    if not isUIOpen then return end
    
    isUIOpen = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        action = "closeShop"
    })
end)

-- ========================================
-- أحداث NUI
-- ========================================

-- شراء سلاح
RegisterNUICallback("buyWeapon", function(data, cb)
    if not currentShop then
        cb({success = false, message = "لا يوجد متجر قريب"})
        return
    end
    
    local purchaseData = {
        weapon = data.weapon,
        price = data.price,
        ammo = data.ammo,
        shopId = currentShop.id
    }
    
    TriggerServerEvent("cb_gunshop:buyWeapon", purchaseData)
    cb({success = true})
end)

-- إغلاق المتجر
RegisterNUICallback("closeShop", function(data, cb)
    TriggerEvent("cb_gunshop:closeUI")
    cb({success = true})
end)

-- البحث عن الأسلحة
RegisterNUICallback("searchWeapons", function(data, cb)
    -- يمكن إضافة منطق البحث هنا إذا لزم الأمر
    cb({success = true})
end)

-- ========================================
-- الخيوط الرئيسية
-- ========================================

-- تهيئة المتاجر
Citizen.CreateThread(function()
    -- تحميل الخط
    LoadFont()
    
    -- إنشاء البليبات والـ NPCs
    for _, shop in ipairs(Config.Shops) do
        -- إنشاء البليب
        local blip = CreateShopBlip(shop)
        shopBlips[shop.id] = blip
        
        -- إنشاء NPC
        local npc = CreateShopNPC(shop)
        if npc then
            shopNPCs[shop.id] = npc
        end
    end
end)

-- خيط التحقق من المسافة
Citizen.CreateThread(function()
    while true do
        local sleep = Config.Performance.RefreshRate
        
        CheckShopDistances()
        
        Wait(sleep)
    end
end)

-- خيط رسم الماركرات والنصوص
Citizen.CreateThread(function()
    while true do
        local sleep = 500
        
        if #nearbyShops > 0 then
            sleep = 0
            
            for _, shopData in ipairs(nearbyShops) do
                local shop = shopData.shop
                local distance = shopData.distance
                
                if distance <= Config.Performance.MarkerDistance then
                    -- رسم الماركر
                    local marker = shop.marker
                    DrawMarker(
                        marker.type,
                        shop.coords.x, shop.coords.y, shop.coords.z,
                        0.0, 0.0, 0.0,
                        0.0, 0.0, 0.0,
                        marker.size.x, marker.size.y, marker.size.z,
                        marker.color.r, marker.color.g, marker.color.b, marker.color.a,
                        marker.bobUpAndDown,
                        marker.faceCamera,
                        2,
                        marker.rotate,
                        nil, nil,
                        false
                    )
                    
                    -- رسم النص
                    DrawText3D(shop.coords.x, shop.coords.y, shop.coords.z + 1.0, Config.UI.MarkerText)
                end
            end
        end
        
        Wait(sleep)
    end
end)

-- خيط التفاعل
Citizen.CreateThread(function()
    while true do
        local sleep = 100
        
        if currentShop and not isUIOpen then
            sleep = 0
            
            -- عرض رسالة المساعدة
            SetTextComponentFormat("STRING")
            AddTextComponentString(Config.UI.MarkerText)
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            
            -- التحقق من الضغط على زر التفاعل
            if IsControlJustReleased(0, 38) then -- E key
                TriggerServerEvent("cb_gunshop:openShop", currentShop.id)
            end
        end
        
        Wait(sleep)
    end
end)

-- خيط إدارة الموارد
Citizen.CreateThread(function()
    while true do
        -- تنظيف الذاكرة كل 5 دقائق
        Wait(300000)
        
        -- إعادة تحميل النماذج إذا لزم الأمر
        for shopId, npc in pairs(shopNPCs) do
            if not DoesEntityExist(npc) then
                local shop = nil
                for _, s in ipairs(Config.Shops) do
                    if s.id == shopId then
                        shop = s
                        break
                    end
                end
                
                if shop then
                    shopNPCs[shopId] = CreateShopNPC(shop)
                end
            end
        end
    end
end)

-- ========================================
-- أحداث التنظيف
-- ========================================

-- تنظيف الموارد عند إغلاق المورد
AddEventHandler("onResourceStop", function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    -- حذف البليبات
    for _, blip in pairs(shopBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    
    -- حذف NPCs
    for _, npc in pairs(shopNPCs) do
        if DoesEntityExist(npc) then
            DeleteEntity(npc)
        end
    end
    
    -- إغلاق الواجهة
    if isUIOpen then
        SetNuiFocus(false, false)
    end
end)

-- ========================================
-- دوال مساعدة إضافية
-- ========================================

-- الحصول على معلومات المتجر الحالي
function GetCurrentShop()
    return currentShop
end

-- التحقق من حالة الواجهة
function IsShopUIOpen()
    return isUIOpen
end

-- إغلاق الواجهة يدوياً
function ForceCloseShopUI()
    TriggerEvent("cb_gunshop:closeUI")
end

-- تصدير الدوال للموارد الأخرى
exports("GetCurrentShop", GetCurrentShop)
exports("IsShopUIOpen", IsShopUIOpen)
exports("ForceCloseShopUI", ForceCloseShopUI)
