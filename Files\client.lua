local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

-- VRP Interface
vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP")

-- Variables
local isInShop = false
local currentShop = nil
local shopPeds = {}
local shopBlips = {}
local nearbyShops = {}

-- Initialize
Citizen.CreateThread(function()
    -- Wait for game to load
    while not HasCollisionLoadedAroundEntity(PlayerPedId()) do
        Citizen.Wait(1000)
    end
    
    -- Create blips and peds
    CreateShopBlips()
    CreateShopPeds()
    
    print("^2[CB_GunShop]^7 Client loaded successfully!")
end)

-- Create shop blips
function CreateShopBlips()
    for k, shop in pairs(Config.Shops) do
        if shop.blip then
            local blip = AddBlipForCoord(shop.coords.x, shop.coords.y, shop.coords.z)
            SetBlipSprite(blip, shop.blip.sprite)
            SetBlipColour(blip, shop.blip.color)
            SetBlipScale(blip, shop.blip.scale)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(shop.blip.name)
            EndTextCommandSetBlipName(blip)
            shopBlips[k] = blip
        end
    end
end

-- Create shop peds
function CreateShopPeds()
    for k, shop in pairs(Config.Shops) do
        if shop.ped then
            RequestModel(GetHashKey(shop.ped.model))
            while not HasModelLoaded(GetHashKey(shop.ped.model)) do
                Citizen.Wait(1)
            end
            
            local ped = CreatePed(4, GetHashKey(shop.ped.model), 
                shop.ped.coords.x, shop.ped.coords.y, shop.ped.coords.z, 
                shop.ped.heading, false, true)
            
            SetEntityCanBeDamaged(ped, false)
            SetPedCanRagdollFromPlayerImpact(ped, false)
            TaskSetBlockingOfNonTemporaryEvents(ped, true)
            SetPedFleeAttributes(ped, 0, 0)
            SetPedCombatAttributes(ped, 17, 1)
            SetPedRandomComponentVariation(ped, true)
            TaskStartScenarioInPlace(ped, "WORLD_HUMAN_STAND_IMPATIENT", 0, true)
            
            shopPeds[k] = ped
        end
    end
end

-- Main distance checking thread
Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        
        nearbyShops = {}
        isInShop = false
        currentShop = nil
        
        for k, shop in pairs(Config.Shops) do
            local distance = #(coords - shop.coords)
            
            if distance < Config.DrawDistance then
                table.insert(nearbyShops, {id = k, shop = shop, distance = distance})
                
                if distance < Config.InteractDistance then
                    isInShop = true
                    currentShop = shop
                    sleep = 0
                end
            end
        end
        
        Citizen.Wait(sleep)
    end
end)

-- Marker and interaction thread
Citizen.CreateThread(function()
    while true do
        local sleep = 500
        
        if #nearbyShops > 0 then
            sleep = 0
            
            for _, shopData in pairs(nearbyShops) do
                local shop = shopData.shop
                local distance = shopData.distance
                
                if distance < Config.MarkerDistance then
                    -- Draw marker
                    local marker = shop.marker
                    DrawMarker(marker.type, 
                        shop.coords.x, shop.coords.y, shop.coords.z - 1.0,
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                        marker.size.x, marker.size.y, marker.size.z,
                        marker.color.r, marker.color.g, marker.color.b, marker.color.a,
                        false, true, 2, false, nil, nil, false)
                    
                    -- Draw 3D text
                    if distance < Config.InteractDistance then
                        Draw3DText(shop.coords.x, shop.coords.y, shop.coords.z + 1.0, 
                            Config.Messages['press_to_open'])
                    end
                end
            end
        end
        
        Citizen.Wait(sleep)
    end
end)

-- Interaction thread
Citizen.CreateThread(function()
    while true do
        local sleep = 100
        
        if isInShop and currentShop then
            sleep = 0
            
            if IsControlJustReleased(0, Config.InteractKey) then
                OpenShop()
            end
        end
        
        Citizen.Wait(sleep)
    end
end)

-- Anti-cheat thread
Citizen.CreateThread(function()
    if not Config.AntiCheat.enabled then return end
    
    while true do
        if isInShop then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - currentShop.coords)
            
            if distance > Config.AntiCheat.maxDistance then
                CloseShop()
                TriggerEvent('chat:addMessage', {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"نظام الأمان", Config.Messages['too_far']}
                })
            end
        end
        
        Citizen.Wait(Config.AntiCheat.checkInterval)
    end
end)

-- Functions
function OpenShop()
    if currentShop then
        TriggerServerEvent('gunshop:requestOpen', currentShop.id)
    end
end

function CloseShop()
    isInShop = false
    SetNuiFocus(false, false)
    SendNUIMessage({action = 'close'})
    
    if Config.EnableSounds then
        PlaySoundFrontend(-1, "CANCEL", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
    end
end

function Draw3DText(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    if onScreen then
        local scale = 0.35
        SetTextScale(scale, scale)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextDropshadow(2, 0, 0, 0, 200)
        SetTextEdge(1, 0, 0, 0, 255)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

function PlayPurchaseSound(success)
    if Config.EnableSounds then
        if success then
            PlaySoundFrontend(-1, "PURCHASE", "HUD_LIQUOR_STORE_SOUNDSET", 1)
        else
            PlaySoundFrontend(-1, "ERROR", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
        end
    end
end

-- Events
RegisterNetEvent('gunshop:openUI')
AddEventHandler('gunshop:openUI', function(playerMoney, weapons, shopData)
    isInShop = true
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        action = 'open',
        money = playerMoney,
        weapons = weapons,
        shopName = shopData.name,
        serverName = Config.ServerName,
        serverLogo = Config.ServerLogo,
        ammoPrice = Config.AmmoPrice,
        maxAmmo = Config.MaxAmmo,
        defaultAmmo = Config.DefaultAmmo
    })
    
    if Config.EnableSounds then
        PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
    end
end)

RegisterNetEvent('gunshop:purchaseResult')
AddEventHandler('gunshop:purchaseResult', function(success, message, newMoney)
    PlayPurchaseSound(success)
    
    SendNUIMessage({
        action = 'purchaseResult',
        success = success,
        message = message,
        money = newMoney or 0
    })
    
    -- Show notification
    if success then
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"متجر الأسلحة", message}
        })
    else
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"متجر الأسلحة", message}
        })
    end
end)

RegisterNetEvent('gunshop:notification')
AddEventHandler('gunshop:notification', function(message, type)
    local color = type == 'success' and {0, 255, 0} or {255, 0, 0}
    TriggerEvent('chat:addMessage', {
        color = color,
        multiline = true,
        args = {"متجر الأسلحة", message}
    })
end)

-- NUI Callbacks
RegisterNUICallback('close', function(data, cb)
    CloseShop()
    cb('ok')
end)

RegisterNUICallback('buyWeapon', function(data, cb)
    if not currentShop then
        cb('error')
        return
    end
    
    TriggerServerEvent('gunshop:buyWeapon', {
        shopId = currentShop.id,
        weapon = data.weapon,
        ammo = data.ammo
    })
    cb('ok')
end)

RegisterNUICallback('playSound', function(data, cb)
    if Config.EnableSounds and data.sound then
        PlaySoundFrontend(-1, data.sound, "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
    end
    cb('ok')
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Remove blips
        for k, blip in pairs(shopBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        
        -- Remove peds
        for k, ped in pairs(shopPeds) do
            if DoesEntityExist(ped) then
                DeleteEntity(ped)
            end
        end
        
        -- Close UI
        if isInShop then
            CloseShop()
        end
    end
end)