# دليل التثبيت السريع - CB Gun Shop

## 🚀 التثبيت السريع (5 دقائق)

### 1. نسخ الملفات
```bash
# انسخ الملفات الجديدة إلى مجلد الموارد
cp new_config.lua config.lua
cp new_server.lua server.lua  
cp new_client.lua client.lua
cp new_fxmanifest.lua fxmanifest.lua
cp -r new_ui/ ui/
```

### 2. تحديث server.cfg
```lua
# أضف هذا السطر إلى server.cfg
ensure CB_GunShop
```

### 3. إعداد الصلاحيات في vRP
```lua
-- أضف هذه الصلاحيات إلى groups.lua

-- للمواطنين العاديين
["citizen"] = {
    "citizen.basic",
    "gunshop.access"
},

-- للشرطة
["police"] = {
    "police.officer", 
    "police.armory"
},

-- للـ VIP
["vip"] = {
    "vip.premium"
},

-- للإدارة
["admin"] = {
    "server.owner",
    "blackmarket.access"
}
```

### 4. إضافة صور الأسلحة
```bash
# ضع صور الأسلحة في مجلد
new_ui/img/

# أسماء الصور يجب أن تطابق أسماء الأسلحة
weapon_pistol.png
weapon_assaultrifle.png
weapon_knife.png
# إلخ...
```

### 5. إعادة تشغيل المورد
```bash
# في وحدة تحكم الخادم
restart CB_GunShop
```

## ⚙️ إعدادات سريعة

### تغيير مواقع المتاجر
```lua
-- في config.lua، قسم Config.Shops
coords = vector3(x, y, z), -- إحداثيات جديدة
```

### تغيير أسعار الأسلحة
```lua
-- في config.lua، قسم Config.WeaponDatabase
price = 5000, -- السعر الجديد
```

### تغيير ساعات العمل
```lua
-- في إعدادات المتجر
openHours = {startHour = 6, endHour = 22}, -- من 6 صباحاً إلى 10 مساءً
```

### تفعيل/إلغاء الخصومات
```lua
-- في إعدادات المتجر
discountPercentage = 20, -- خصم 20%
-- أو
discountPercentage = -25, -- زيادة 25% في السعر
```

## 🔧 حل المشاكل السريع

### المتجر لا يفتح
```lua
-- تحقق من:
1. الصلاحيات في vRP
2. ساعات العمل
3. وحدة التحكم للأخطاء
```

### الصور لا تظهر
```bash
# تأكد من:
1. وجود الصور في new_ui/img/
2. أسماء الصور صحيحة
3. صيغة الصور PNG أو JPG
```

### مشاكل في الأداء
```lua
-- قلل هذه القيم في config.lua
Config.Performance = {
    CheckDistance = 10.0,    -- بدلاً من 15.0
    RefreshRate = 1000       -- بدلاً من 500
}
```

## 📱 اختبار سريع

### 1. اختبار الوصول
- اذهب إلى إحداثيات المتجر
- يجب أن ترى ماركر وبليب
- اضغط E للدخول

### 2. اختبار الشراء
- ابحث عن سلاح
- اختر كمية الذخيرة
- اضغط تأكيد الشراء

### 3. اختبار الصلاحيات
- جرب مع مستخدمين مختلفين
- تأكد من عمل نظام الصلاحيات

## 🎯 نصائح للأداء الأمثل

### للخوادم الكبيرة (100+ لاعب)
```lua
Config.Performance = {
    CheckDistance = 8.0,
    MarkerDistance = 5.0,
    RefreshRate = 1000,
    BlipRefreshRate = 10000
}
```

### للخوادم الصغيرة (أقل من 50 لاعب)
```lua
Config.Performance = {
    CheckDistance = 20.0,
    MarkerDistance = 12.0,
    RefreshRate = 250,
    BlipRefreshRate = 2000
}
```

## 🔒 إعدادات الأمان

### تفعيل مكافحة الغش
```lua
Config.Security = {
    EnableAntiCheat = true,
    MaxPurchasePerMinute = 3,
    LogTransactions = true
}
```

### تفعيل القائمة السوداء
```lua
Config.Security = {
    BlacklistEnabled = true,
    -- أضف معرفات اللاعبين المحظورين
}
```

## 📊 مراقبة النظام

### تفعيل السجلات
```lua
Config.Security.LogTransactions = true
Config.Debug = true -- للتطوير فقط
```

### مراجعة السجلات
```sql
-- في قاعدة البيانات
SELECT * FROM gunshop_logs ORDER BY timestamp DESC LIMIT 100;
```

## 🆘 الدعم السريع

### مشاكل شائعة وحلولها

| المشكلة | الحل |
|---------|------|
| المتجر لا يفتح | تحقق من الصلاحيات |
| الصور مفقودة | ضع الصور في new_ui/img/ |
| بطء في الأداء | قلل CheckDistance |
| أخطاء في وحدة التحكم | تحقق من fxmanifest.lua |

### أوامر مفيدة
```bash
# إعادة تشغيل المورد
restart CB_GunShop

# مراجعة الأخطاء
monitor

# تحديث الموارد
refresh
```

---

**للدعم الفني**: تواصل معنا عبر Discord أو GitHub Issues
