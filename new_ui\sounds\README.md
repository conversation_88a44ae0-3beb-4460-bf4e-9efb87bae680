# مجلد الأصوات

## 🔊 الأصوات المطلوبة (اختيارية)

يمكنك إضافة أصوات لتحسين تجربة المستخدم:

### أصوات الواجهة
- `ui_open.mp3` - صوت فتح المتجر
- `ui_close.mp3` - صوت إغلاق المتجر
- `ui_click.mp3` - صوت النقر على الأزرار
- `ui_hover.mp3` - صوت التمرير على الأزرار

### أصوات المعاملات
- `purchase_success.mp3` - صوت نجاح الشراء
- `purchase_fail.mp3` - صوت فشل الشراء
- `money_count.mp3` - صوت عد النقود

### أصوات التنبيهات
- `notification.mp3` - صوت التنبيهات العامة
- `error.mp3` - صوت تنبيهات الخطأ
- `warning.mp3` - صوت تنبيهات التحذير

## 📐 مواصفات الأصوات

- **الصيغة**: MP3 أو WAV
- **الجودة**: 128kbps أو أعلى
- **المدة**: قصيرة (1-3 ثواني)
- **الحجم**: صغير لتوفير الباندويث

## 🎵 نصائح للأصوات

1. **استخدم أصوات قصيرة** لتجنب الإزعاج
2. **اختر أصوات واضحة** وغير مشوشة
3. **تأكد من التوافق** مع جميع المتصفحات
4. **اختبر مستوى الصوت** ليكون مناسباً

## 🔧 التفعيل في الكود

لتفعيل الأصوات، عدّل في `script.js`:

```javascript
// تشغيل صوت
function playSound(soundName) {
    if (Config.UI.SoundEffects) {
        const audio = new Audio(`sounds/${soundName}.mp3`);
        audio.volume = 0.5; // مستوى الصوت
        audio.play().catch(e => console.log('Sound play failed:', e));
    }
}

// استخدام الصوت
playSound('ui_click'); // عند النقر
playSound('purchase_success'); // عند نجاح الشراء
```

## ⚠️ ملاحظة

الأصوات اختيارية ويمكن للنظام العمل بدونها. إذا لم تضع أصوات، لن تظهر أي أخطاء.
