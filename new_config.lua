-- ========================================
-- CB Gun Shop - Configuration File
-- نظام متجر الأسلحة المطور
-- ========================================

Config = {}

-- ========================================
-- إعدادات عامة
-- ========================================
Config.Locale = 'ar' -- اللغة (ar/en)
Config.Currency = '$' -- رمز العملة
Config.Debug = false -- وضع التطوير

-- ========================================
-- إعدادات الأداء
-- ========================================
Config.Performance = {
    CheckDistance = 15.0,      -- مسافة التحقق من القرب من المتجر
    MarkerDistance = 8.0,      -- مسافة إظهار الماركر
    InteractionDistance = 2.5, -- مسافة التفاعل
    RefreshRate = 500,         -- معدل التحديث (ميلي ثانية)
    BlipRefreshRate = 5000     -- معدل تحديث البليب
}

-- ========================================
-- إعدادات الواجهة
-- ========================================
Config.UI = {
    Font = "Cairo",                    -- خط الواجهة
    MarkerFont = "pixel",              -- خط الماركر
    MarkerText = "اضغط [E] للدخول",   -- نص الماركر
    ServerName = "CB Store",           -- اسم السيرفر
    ServerLogo = "https://i.imgur.com/example.png", -- لوجو السيرفر
    Theme = "dark",                    -- السمة (dark/light)
    Animations = true,                 -- تفعيل الحركات
    SoundEffects = true               -- تفعيل الأصوات
}

-- ========================================
-- إعدادات الأسلحة والذخيرة
-- ========================================
Config.Weapons = {
    AmmoPrice = 5,          -- سعر الطلقة الواحدة
    MaxAmmo = 250,          -- أقصى كمية ذخيرة
    MinAmmo = 0,            -- أقل كمية ذخيرة
    DefaultAmmo = 30,       -- الكمية الافتراضية
    AllowRefund = false,    -- السماح بالاسترداد
    RefundPercentage = 50   -- نسبة الاسترداد
}

-- ========================================
-- إعدادات الأمان
-- ========================================
Config.Security = {
    EnableAntiCheat = true,        -- تفعيل مكافحة الغش
    MaxPurchasePerMinute = 5,      -- أقصى عدد مشتريات في الدقيقة
    LogTransactions = true,        -- تسجيل المعاملات
    RequirePermission = true,      -- طلب الصلاحيات
    BlacklistEnabled = true,       -- تفعيل القائمة السوداء
    WhitelistMode = false          -- وضع القائمة البيضاء
}

-- ========================================
-- فئات الأسلحة
-- ========================================
Config.WeaponCategories = {
    {
        id = "pistol",
        name = "مسدسات",
        icon = "🔫",
        description = "أسلحة يدوية خفيفة",
        color = "#4CAF50"
    },
    {
        id = "rifle",
        name = "بنادق هجومية",
        icon = "🔫",
        description = "بنادق قوية ومتوسطة المدى",
        color = "#FF9800"
    },
    {
        id = "smg",
        name = "رشاشات",
        icon = "🔫",
        description = "أسلحة سريعة الإطلاق",
        color = "#2196F3"
    },
    {
        id = "sniper",
        name = "قناصة",
        icon = "🎯",
        description = "أسلحة بعيدة المدى",
        color = "#9C27B0"
    },
    {
        id = "shotgun",
        name = "بنادق الخرطوش",
        icon = "💥",
        description = "أسلحة قريبة المدى",
        color = "#F44336"
    },
    {
        id = "mg",
        name = "رشاشات ثقيلة",
        icon = "⚡",
        description = "أسلحة ثقيلة عالية الضرر",
        color = "#795548"
    },
    {
        id = "melee",
        name = "أسلحة بيضاء",
        icon = "⚔️",
        description = "أسلحة قتال قريب",
        color = "#607D8B"
    },
    {
        id = "throwable",
        name = "قنابل ومتفجرات",
        icon = "💣",
        description = "أسلحة قابلة للرمي",
        color = "#E91E63"
    }
}

-- ========================================
-- قاعدة بيانات الأسلحة الكاملة
-- ========================================
Config.WeaponDatabase = {
    -- مسدسات
    {
        name = "weapon_pistol",
        label = "مسدس عادي",
        category = "pistol",
        price = 2500,
        image = "weapon_pistol.png",
        ammo = "AMMO_PISTOL",
        stats = {
            damage = 65,
            accuracy = 75,
            range = 40,
            fireRate = 60
        },
        description = "مسدس عادي للاستخدام اليومي",
        legal = true,
        vip = false
    },
    {
        name = "weapon_combatpistol",
        label = "مسدس قتالي",
        category = "pistol",
        price = 3500,
        image = "weapon_combatpistol.png",
        ammo = "AMMO_PISTOL",
        stats = {
            damage = 70,
            accuracy = 80,
            range = 45,
            fireRate = 65
        },
        description = "مسدس قتالي محسن للمعارك",
        legal = false,
        vip = false
    },
    {
        name = "weapon_appistol",
        label = "مسدس AP",
        category = "pistol",
        price = 4000,
        image = "weapon_appistol.png",
        ammo = "AMMO_PISTOL",
        stats = {
            damage = 75,
            accuracy = 85,
            range = 50,
            fireRate = 70
        },
        description = "مسدس خارق للدروع",
        legal = false,
        vip = true
    },

    -- بنادق هجومية
    {
        name = "weapon_assaultrifle",
        label = "بندقية هجومية",
        category = "rifle",
        price = 15000,
        image = "weapon_assaultrifle.png",
        ammo = "AMMO_RIFLE",
        stats = {
            damage = 85,
            accuracy = 80,
            range = 85,
            fireRate = 75
        },
        description = "بندقية هجومية قوية ومتعددة الاستخدامات",
        legal = false,
        vip = false
    },
    {
        name = "weapon_carbinerifle",
        label = "بندقية كاربين",
        category = "rifle",
        price = 12000,
        image = "weapon_carbinerifle.png",
        ammo = "AMMO_RIFLE",
        stats = {
            damage = 80,
            accuracy = 85,
            range = 80,
            fireRate = 80
        },
        description = "بندقية كاربين سريعة ودقيقة",
        legal = false,
        vip = false
    },

    -- رشاشات
    {
        name = "weapon_microsmg",
        label = "رشاش صغير",
        category = "smg",
        price = 8000,
        image = "weapon_microsmg.png",
        ammo = "AMMO_SMG",
        stats = {
            damage = 60,
            accuracy = 65,
            range = 35,
            fireRate = 90
        },
        description = "رشاش صغير سريع الإطلاق",
        legal = false,
        vip = false
    },

    -- قناصة
    {
        name = "weapon_sniperrifle",
        label = "بندقية قناصة",
        category = "sniper",
        price = 35000,
        image = "weapon_sniperrifle.png",
        ammo = "AMMO_SNIPER",
        stats = {
            damage = 95,
            accuracy = 95,
            range = 100,
            fireRate = 20
        },
        description = "بندقية قناصة عالية الدقة",
        legal = false,
        vip = true
    },

    -- أسلحة بيضاء
    {
        name = "weapon_knife",
        label = "سكين",
        category = "melee",
        price = 500,
        image = "weapon_knife.png",
        ammo = nil,
        stats = {
            damage = 50,
            accuracy = 100,
            range = 5,
            fireRate = 100
        },
        description = "سكين حاد للقتال القريب",
        legal = true,
        vip = false
    },
    {
        name = "weapon_bat",
        label = "مضرب بيسبول",
        category = "melee",
        price = 300,
        image = "weapon_bat.png",
        ammo = nil,
        stats = {
            damage = 45,
            accuracy = 90,
            range = 8,
            fireRate = 80
        },
        description = "مضرب بيسبول للدفاع عن النفس",
        legal = true,
        vip = false
    }
}

-- ========================================
-- إعدادات المتاجر
-- ========================================
Config.Shops = {
    {
        id = "downtown_gunshop",
        name = "متجر الأسلحة - وسط المدينة",
        coords = vector3(22.56, -1105.36, 29.8),
        heading = 160.0,

        -- إعدادات البليب
        blip = {
            sprite = 110,
            color = 1,
            scale = 0.8,
            shortRange = true
        },

        -- إعدادات الماركر
        marker = {
            type = 1,
            size = {x = 1.5, y = 1.5, z = 1.0},
            color = {r = 255, g = 255, b = 255, a = 100},
            bobUpAndDown = true,
            faceCamera = false,
            rotate = true
        },

        -- الصلاحيات المطلوبة
        permissions = {
            "gunshop.access",
            "citizen.basic"
        },

        -- فئات الأسلحة المتاحة
        allowedCategories = {"pistol", "melee"},

        -- أسلحة محددة (اختياري)
        specificWeapons = nil,

        -- إعدادات خاصة
        settings = {
            requireLicense = true,
            maxPurchasePerDay = 3,
            discountPercentage = 0,
            openHours = {startHour = 6, endHour = 22}, -- من 6 صباحاً إلى 10 مساءً
            npcModel = "s_m_y_ammucity_01"
        }
    },

    {
        id = "police_armory",
        name = "مخزن أسلحة الشرطة",
        coords = vector3(451.51, -979.44, 30.68),
        heading = 90.0,

        blip = {
            sprite = 110,
            color = 3,
            scale = 0.8,
            shortRange = true
        },

        marker = {
            type = 1,
            size = {x = 1.5, y = 1.5, z = 1.0},
            color = {r = 0, g = 100, b = 255, a = 100},
            bobUpAndDown = true,
            faceCamera = false,
            rotate = true
        },

        permissions = {
            "police.armory",
            "police.officer"
        },

        allowedCategories = {"pistol", "rifle", "smg", "shotgun"},

        settings = {
            requireLicense = false,
            maxPurchasePerDay = 10,
            discountPercentage = 50, -- خصم 50% للشرطة
            openHours = {startHour = 0, endHour = 24}, -- مفتوح 24/7
            npcModel = "s_m_y_cop_01"
        }
    },

    {
        id = "black_market",
        name = "السوق السوداء",
        coords = vector3(707.84, -966.87, 30.41),
        heading = 180.0,

        blip = {
            sprite = 110,
            color = 6,
            scale = 0.7,
            shortRange = true
        },

        marker = {
            type = 1,
            size = {x = 1.2, y = 1.2, z = 0.8},
            color = {r = 255, g = 0, b = 0, a = 150},
            bobUpAndDown = true,
            faceCamera = false,
            rotate = true
        },

        permissions = {
            "blackmarket.access",
            "criminal.advanced"
        },

        allowedCategories = {"rifle", "sniper", "mg", "throwable"},

        settings = {
            requireLicense = false,
            maxPurchasePerDay = 2,
            discountPercentage = -25, -- زيادة 25% في السعر
            openHours = {startHour = 22, endHour = 6}, -- من 10 مساءً إلى 6 صباحاً
            npcModel = "g_m_y_mexgang_01"
        }
    },

    {
        id = "vip_gunshop",
        name = "متجر الأسلحة VIP",
        coords = vector3(-662.15, -935.3, 21.82),
        heading = 270.0,

        blip = {
            sprite = 110,
            color = 5,
            scale = 1.0,
            shortRange = true
        },

        marker = {
            type = 1,
            size = {x = 2.0, y = 2.0, z = 1.5},
            color = {r = 255, g = 215, b = 0, a = 120},
            bobUpAndDown = true,
            faceCamera = false,
            rotate = true
        },

        permissions = {
            "vip.premium",
            "vip.gold"
        },

        allowedCategories = "all", -- جميع الفئات

        settings = {
            requireLicense = false,
            maxPurchasePerDay = 5,
            discountPercentage = 20, -- خصم 20% للـ VIP
            openHours = {startHour = 0, endHour = 24}, -- مفتوح 24/7
            npcModel = "s_m_m_highsec_01"
        }
    }
}

-- ========================================
-- إعدادات الرسائل والنصوص
-- ========================================
Config.Messages = {
    ar = {
        -- رسائل عامة
        shop_opened = "تم فتح المتجر",
        shop_closed = "تم إغلاق المتجر",
        insufficient_money = "ليس لديك مال كافي",
        purchase_success = "تم الشراء بنجاح",
        purchase_failed = "فشل في الشراء",

        -- رسائل الصلاحيات
        no_permission = "ليس لديك صلاحية للوصول إلى هذا المتجر",
        license_required = "تحتاج إلى رخصة أسلحة",

        -- رسائل الحدود
        daily_limit_reached = "وصلت إلى الحد الأقصى للشراء اليومي",
        shop_closed_hours = "المتجر مغلق في هذا الوقت",

        -- رسائل الواجهة
        weapon_name = "اسم السلاح",
        weapon_price = "السعر",
        weapon_damage = "الضرر",
        weapon_accuracy = "الدقة",
        weapon_range = "المدى",
        weapon_firerate = "معدل الإطلاق",
        ammo_count = "كمية الذخيرة",
        total_price = "السعر الإجمالي",
        buy_button = "شراء",
        close_button = "إغلاق",
        search_placeholder = "البحث عن سلاح...",
        category_all = "الكل"
    },

    en = {
        -- General messages
        shop_opened = "Shop opened",
        shop_closed = "Shop closed",
        insufficient_money = "Insufficient money",
        purchase_success = "Purchase successful",
        purchase_failed = "Purchase failed",

        -- Permission messages
        no_permission = "You don't have permission to access this shop",
        license_required = "You need a weapon license",

        -- Limit messages
        daily_limit_reached = "Daily purchase limit reached",
        shop_closed_hours = "Shop is closed at this time",

        -- UI messages
        weapon_name = "Weapon Name",
        weapon_price = "Price",
        weapon_damage = "Damage",
        weapon_accuracy = "Accuracy",
        weapon_range = "Range",
        weapon_firerate = "Fire Rate",
        ammo_count = "Ammo Count",
        total_price = "Total Price",
        buy_button = "Buy",
        close_button = "Close",
        search_placeholder = "Search for weapon...",
        category_all = "All"
    }
}

-- ========================================
-- دوال مساعدة
-- ========================================

-- الحصول على رسالة بناءً على اللغة
function Config.GetMessage(key)
    local messages = Config.Messages[Config.Locale] or Config.Messages['ar']
    return messages[key] or key
end

-- التحقق من صلاحية الوصول للمتجر
function Config.CanAccessShop(shopId, playerPermissions)
    local shop = nil
    for _, s in ipairs(Config.Shops) do
        if s.id == shopId then
            shop = s
            break
        end
    end

    if not shop then return false end

    for _, requiredPerm in ipairs(shop.permissions) do
        local hasPermission = false
        for _, playerPerm in ipairs(playerPermissions) do
            if playerPerm == requiredPerm then
                hasPermission = true
                break
            end
        end
        if hasPermission then return true end
    end

    return false
end

-- الحصول على أسلحة متجر معين
function Config.GetShopWeapons(shopId)
    local shop = nil
    for _, s in ipairs(Config.Shops) do
        if s.id == shopId then
            shop = s
            break
        end
    end

    if not shop then return {} end

    local weapons = {}

    if shop.specificWeapons then
        -- أسلحة محددة
        for _, weaponName in ipairs(shop.specificWeapons) do
            for _, weapon in ipairs(Config.WeaponDatabase) do
                if weapon.name == weaponName then
                    table.insert(weapons, weapon)
                    break
                end
            end
        end
    elseif shop.allowedCategories == "all" then
        -- جميع الأسلحة
        weapons = Config.WeaponDatabase
    elseif type(shop.allowedCategories) == "table" then
        -- فئات محددة
        for _, weapon in ipairs(Config.WeaponDatabase) do
            for _, category in ipairs(shop.allowedCategories) do
                if weapon.category == category then
                    table.insert(weapons, weapon)
                    break
                end
            end
        end
    end

    return weapons
end
