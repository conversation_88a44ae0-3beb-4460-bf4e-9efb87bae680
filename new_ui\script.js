// ========================================
// CB Gun Shop - JavaScript
// ========================================

class GunShop {
    constructor() {
        this.currentShop = null;
        this.currentWeapons = [];
        this.filteredWeapons = [];
        this.selectedWeapon = null;
        this.playerMoney = 0;

        // Performance optimizations
        this.searchTimeout = null;
        this.renderTimeout = null;
        this.lastRenderTime = 0;
        this.isRendering = false;

        // Cache DOM elements
        this.domCache = {};

        this.init();
    }
    
    init() {
        this.cacheDOMElements();
        this.bindEvents();
        this.hideLoadingScreen();
    }

    cacheDOMElements() {
        this.domCache = {
            closeBtn: $('#closeBtn'),
            searchInput: $('#searchInput'),
            weaponsGrid: $('#weaponsGrid'),
            serverName: $('#serverName'),
            serverLogo: $('#serverLogo'),
            shopName: $('#shopName'),
            playerMoney: $('#playerMoney'),
            purchaseModal: $('#purchaseModal'),
            modalWeaponImage: $('#modalWeaponImage'),
            modalWeaponName: $('#modalWeaponName'),
            modalWeaponDescription: $('#modalWeaponDescription'),
            ammoSlider: $('#ammoSlider'),
            ammoValue: $('#ammoValue'),
            weaponPrice: $('#weaponPrice'),
            ammoPrice: $('#ammoPrice'),
            totalPrice: $('#totalPrice'),
            confirmPurchase: $('#confirmPurchase'),
            notificationContainer: $('#notificationContainer'),
            shopContainer: $('#shopContainer'),
            loadingScreen: $('#loadingScreen')
        };
    }
    
    bindEvents() {
        // Close button
        $('#closeBtn').on('click', () => this.closeShop());
        
        // Search functionality with debouncing
        this.domCache.searchInput.on('input', (e) => this.debouncedSearch(e.target.value));
        
        // Filter buttons
        $(document).on('click', '.filter-btn', (e) => this.filterByCategory(e.target.dataset.category));
        
        // Weapon card clicks
        $(document).on('click', '.weapon-card', (e) => {
            const weaponName = e.currentTarget.dataset.weapon;
            this.openPurchaseModal(weaponName);
        });
        
        // Modal events
        $('.modal-close, #cancelPurchase').on('click', () => this.closePurchaseModal());
        $('#confirmPurchase').on('click', () => this.confirmPurchase());
        
        // Ammo slider
        $('#ammoSlider').on('input', (e) => this.updateAmmoValue(e.target.value));
        
        // Click outside modal to close
        $('.modal').on('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closePurchaseModal();
            }
        });
        
        // Keyboard shortcuts
        $(document).on('keydown', (e) => {
            if (e.key === 'Escape') {
                if ($('#purchaseModal').is(':visible')) {
                    this.closePurchaseModal();
                } else {
                    this.closeShop();
                }
            }
        });
    }
    
    hideLoadingScreen() {
        setTimeout(() => {
            $('#loadingScreen').fadeOut(500);
        }, 1000);
    }
    
    openShop(data) {
        this.currentShop = data.shop;
        this.currentWeapons = data.weapons;
        this.filteredWeapons = [...this.currentWeapons];
        this.playerMoney = data.playerMoney;
        
        // Update UI
        $('#serverName').text(data.serverInfo.name);
        $('#serverLogo').attr('src', data.serverInfo.logo);
        $('#shopName').text(this.currentShop.name);
        $('#playerMoney').text(this.formatMoney(this.playerMoney));
        
        // Generate category filters
        this.generateCategoryFilters();
        
        // Render weapons
        this.renderWeapons();
        
        // Show shop
        $('#shopContainer').fadeIn(500).addClass('fade-in');
    }
    
    closeShop() {
        $('#shopContainer').fadeOut(300, () => {
            this.reset();
        });
        
        // Notify FiveM
        $.post('http://cb_gunshop/closeShop', JSON.stringify({}));
    }
    
    reset() {
        this.currentShop = null;
        this.currentWeapons = [];
        this.filteredWeapons = [];
        this.selectedWeapon = null;
        this.playerMoney = 0;
        
        $('#weaponsGrid').empty();
        $('#searchInput').val('');
        $('.filter-btn').removeClass('active');
        $('.filter-btn[data-category="all"]').addClass('active');
    }
    
    generateCategoryFilters() {
        const categories = [...new Set(this.currentWeapons.map(w => w.category))];
        const filterContainer = $('.filter-container');
        
        // Clear existing filters except "All"
        filterContainer.find('.filter-btn:not([data-category="all"])').remove();
        
        // Add category filters
        categories.forEach(category => {
            const categoryInfo = this.getCategoryInfo(category);
            if (categoryInfo) {
                const filterBtn = $(`
                    <button class="filter-btn" data-category="${category}">
                        <i class="${categoryInfo.icon}"></i>
                        ${categoryInfo.name}
                    </button>
                `);
                filterContainer.append(filterBtn);
            }
        });
    }
    
    getCategoryInfo(category) {
        const categories = {
            'pistol': { name: 'مسدسات', icon: 'fas fa-crosshairs' },
            'rifle': { name: 'بنادق', icon: 'fas fa-bullseye' },
            'smg': { name: 'رشاشات', icon: 'fas fa-fire' },
            'sniper': { name: 'قناصة', icon: 'fas fa-eye' },
            'shotgun': { name: 'خرطوش', icon: 'fas fa-explosion' },
            'mg': { name: 'رشاشات ثقيلة', icon: 'fas fa-bolt' },
            'melee': { name: 'أسلحة بيضاء', icon: 'fas fa-sword' },
            'throwable': { name: 'متفجرات', icon: 'fas fa-bomb' }
        };
        
        return categories[category];
    }
    
    renderWeapons() {
        this.isRendering = true;
        const grid = this.domCache.weaponsGrid;
        grid.empty();
        
        if (this.filteredWeapons.length === 0) {
            grid.append(`
                <div class="no-weapons">
                    <i class="fas fa-search"></i>
                    <p>لا توجد أسلحة متاحة</p>
                </div>
            `);
            return;
        }
        
        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();

        this.filteredWeapons.forEach((weapon, index) => {
            const card = this.createWeaponCard(weapon, index);
            fragment.appendChild(card[0]); // Get DOM element from jQuery object
        });

        grid[0].appendChild(fragment);
        this.isRendering = false;
    }
    
    createWeaponCard(weapon, index) {
        const stats = weapon.stats || {};
        const badges = this.createBadges(weapon);
        
        return $(`
            <div class="weapon-card slide-up" data-weapon="${weapon.name}" style="animation-delay: ${index * 0.1}s">
                ${badges}
                <img src="img/${weapon.image}" alt="${weapon.label}" class="weapon-image" 
                     onerror="this.src='img/default_weapon.png'">
                
                <div class="weapon-info">
                    <h3 class="weapon-name">${weapon.label}</h3>
                    <p class="weapon-description">${weapon.description || 'سلاح عالي الجودة'}</p>
                    <div class="weapon-price">${this.formatMoney(weapon.price)}</div>
                    
                    <div class="weapon-stats-mini">
                        <div class="stat-mini">
                            <span class="label">ضرر</span>
                            <span class="value">${stats.damage || 0}</span>
                        </div>
                        <div class="stat-mini">
                            <span class="label">دقة</span>
                            <span class="value">${stats.accuracy || 0}</span>
                        </div>
                        <div class="stat-mini">
                            <span class="label">مدى</span>
                            <span class="value">${stats.range || 0}</span>
                        </div>
                        <div class="stat-mini">
                            <span class="label">سرعة</span>
                            <span class="value">${stats.fireRate || 0}</span>
                        </div>
                    </div>
                    
                    <button class="buy-btn">
                        <i class="fas fa-shopping-cart"></i>
                        شراء الآن
                    </button>
                </div>
            </div>
        `);
    }
    
    createBadges(weapon) {
        let badges = '';
        
        if (weapon.vip) {
            badges += '<div class="vip-badge">VIP</div>';
        }
        
        if (weapon.legal !== undefined) {
            badges += weapon.legal 
                ? '<div class="legal-badge">قانوني</div>'
                : '<div class="illegal-badge">غير قانوني</div>';
        }
        
        return badges;
    }
    
    debouncedSearch(query) {
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // Set new timeout for debounced search
        this.searchTimeout = setTimeout(() => {
            this.searchWeapons(query);
        }, 300); // 300ms delay
    }

    searchWeapons(query) {
        const searchTerm = query.toLowerCase().trim();

        if (searchTerm === '') {
            this.filteredWeapons = [...this.currentWeapons];
        } else {
            this.filteredWeapons = this.currentWeapons.filter(weapon =>
                weapon.label.toLowerCase().includes(searchTerm) ||
                weapon.name.toLowerCase().includes(searchTerm) ||
                (weapon.description && weapon.description.toLowerCase().includes(searchTerm))
            );
        }

        this.renderWeaponsOptimized();
    }
    
    filterByCategory(category) {
        // Update active filter button
        $('.filter-btn').removeClass('active');
        $(`.filter-btn[data-category="${category}"]`).addClass('active');
        
        // Filter weapons
        if (category === 'all') {
            this.filteredWeapons = [...this.currentWeapons];
        } else {
            this.filteredWeapons = this.currentWeapons.filter(weapon => weapon.category === category);
        }
        
        this.renderWeaponsOptimized();
    }

    renderWeaponsOptimized() {
        // Prevent multiple simultaneous renders
        if (this.isRendering) {
            return;
        }

        // Throttle rendering to prevent performance issues
        const now = Date.now();
        if (now - this.lastRenderTime < 100) { // 100ms throttle
            if (this.renderTimeout) {
                clearTimeout(this.renderTimeout);
            }
            this.renderTimeout = setTimeout(() => {
                this.renderWeapons();
            }, 100);
            return;
        }

        this.lastRenderTime = now;
        this.renderWeapons();
    }
    
    openPurchaseModal(weaponName) {
        const weapon = this.currentWeapons.find(w => w.name === weaponName);
        if (!weapon) return;
        
        this.selectedWeapon = weapon;
        
        // Update modal content
        $('#modalWeaponImage').attr('src', `img/${weapon.image}`);
        $('#modalWeaponName').text(weapon.label);
        $('#modalWeaponDescription').text(weapon.description || 'سلاح عالي الجودة');
        
        // Update stats
        this.updateWeaponStats(weapon.stats || {});
        
        // Reset ammo slider
        $('#ammoSlider').val(30);
        this.updateAmmoValue(30);
        
        // Update prices
        this.updatePrices();
        
        // Show modal
        $('#purchaseModal').fadeIn(300).css('display', 'flex');
    }
    
    closePurchaseModal() {
        $('#purchaseModal').fadeOut(300);
        this.selectedWeapon = null;
    }
    
    updateWeaponStats(stats) {
        const statElements = {
            damage: '#damageStat',
            accuracy: '#accuracyStat',
            range: '#rangeStat',
            fireRate: '#fireRateStat'
        };
        
        Object.keys(statElements).forEach(stat => {
            const value = stats[stat] || 0;
            const percentage = Math.min(value, 100);
            
            $(statElements[stat]).css('width', `${percentage}%`);
            $(`${statElements[stat].replace('Stat', 'Value')}`).text(value);
        });
    }
    
    updateAmmoValue(value) {
        $('#ammoValue').text(value);
        this.updatePrices();
    }
    
    updatePrices() {
        if (!this.selectedWeapon) return;
        
        const weaponPrice = this.selectedWeapon.price;
        const ammoCount = parseInt($('#ammoSlider').val());
        const ammoPrice = ammoCount * 5; // 5$ per bullet
        const totalPrice = weaponPrice + ammoPrice;
        
        $('#weaponPrice').text(this.formatMoney(weaponPrice));
        $('#ammoPrice').text(this.formatMoney(ammoPrice));
        $('#totalPrice').text(this.formatMoney(totalPrice));
        
        // Check if player can afford
        const canAfford = totalPrice <= this.playerMoney;
        $('#confirmPurchase').prop('disabled', !canAfford);
        
        if (!canAfford) {
            $('#confirmPurchase').addClass('disabled').text('لا يوجد مال كافي');
        } else {
            $('#confirmPurchase').removeClass('disabled').html('<i class="fas fa-shopping-cart"></i> تأكيد الشراء');
        }
    }
    
    confirmPurchase() {
        if (!this.selectedWeapon) return;
        
        const ammoCount = parseInt($('#ammoSlider').val());
        const totalPrice = this.selectedWeapon.price + (ammoCount * 5);
        
        if (totalPrice > this.playerMoney) {
            this.showNotification('لا يوجد مال كافي', 'error');
            return;
        }
        
        // Send purchase request to FiveM
        $.post('http://cb_gunshop/buyWeapon', JSON.stringify({
            weapon: this.selectedWeapon.name,
            price: this.selectedWeapon.price,
            ammo: ammoCount
        }), (response) => {
            if (response.success) {
                this.showNotification('تم الشراء بنجاح!', 'success');
                this.playerMoney -= totalPrice;
                $('#playerMoney').text(this.formatMoney(this.playerMoney));
                this.closePurchaseModal();
            } else {
                this.showNotification(response.message || 'فشل في الشراء', 'error');
            }
        });
    }
    
    formatMoney(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 0
        }).format(amount).replace('ر.س.', '$');
    }
    
    showNotification(message, type = 'info') {
        const notification = $(`
            <div class="notification ${type}">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
                ${message}
            </div>
        `);
        
        $('#notificationContainer').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(300, () => notification.remove());
        }, 3000);
    }
}

// Initialize the gun shop
const gunShop = new GunShop();

// Listen for messages from FiveM
window.addEventListener('message', (event) => {
    const data = event.data;
    
    switch (data.action) {
        case 'openShop':
            gunShop.openShop(data.data);
            break;
            
        case 'closeShop':
            gunShop.closeShop();
            break;
            
        case 'updateMoney':
            gunShop.playerMoney = data.money;
            $('#playerMoney').text(gunShop.formatMoney(data.money));
            break;
            
        case 'notification':
            gunShop.showNotification(data.message, data.type);
            break;
    }
});

// Disable right-click context menu
$(document).on('contextmenu', () => false);

// Disable F12 and other dev tools shortcuts
$(document).on('keydown', (e) => {
    if (e.key === 'F12' || 
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
        return false;
    }
});
