@echo off
echo ========================================
echo CB Gun Shop - Quick Setup
echo ========================================
echo.

echo [1/5] Creating backup of old files...
if exist config.lua (
    copy config.lua config_old.lua
    echo ✓ Backed up config.lua
)

if exist Files\client.lua (
    copy Files\client.lua Files\client_old.lua
    echo ✓ Backed up client.lua
)

if exist Files\server.lua (
    copy Files\server.lua Files\server_old.lua
    echo ✓ Backed up server.lua
)

echo.
echo [2/5] Copying new files...
copy new_config.lua config.lua
copy new_client.lua Files\client.lua
copy new_server.lua Files\server.lua
echo ✓ New files copied

echo.
echo [3/5] Creating UI directory...
if not exist ui mkdir ui
xcopy new_ui ui /E /I /Y
echo ✓ UI files copied

echo.
echo [4/5] Creating default weapon images...
if not exist ui\img mkdir ui\img
echo. > ui\img\weapon_pistol.png
echo. > ui\img\weapon_assaultrifle.png
echo. > ui\img\weapon_knife.png
echo. > ui\img\default_weapon.png
echo ✓ Default images created

echo.
echo [5/5] Setup complete!
echo.
echo ========================================
echo NEXT STEPS:
echo ========================================
echo 1. Add weapon images to: ui\img\
echo 2. Restart your FiveM server
echo 3. Add permissions to vRP groups.lua:
echo    ["citizen"] = { "citizen.basic", "gunshop.access" }
echo 4. Test the shops at these locations:
echo    - Downtown: 45, -1747, 29
echo    - Police: 451, -979, 30
echo    - Airport: -2130, -570, 18
echo.
echo Press any key to exit...
pause >nul
