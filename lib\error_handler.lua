-- ========================================
-- CB Gun Shop - Error <PERSON>ler
-- نظام معالجة الأخطاء والسجلات
-- ========================================

local ErrorHandler = {}
local Utils = require('lib/utils')
local Constants = require('lib/constants')

-- ========================================
-- متغيرات النظام
-- ========================================

local errorLogs = {}
local errorCounts = {}
local lastErrorTime = {}
local isDebugMode = false

-- ========================================
-- إعدادات معالجة الأخطاء
-- ========================================

local ERROR_CONFIG = {
    MAX_LOGS = 1000,                    -- أقصى عدد سجلات
    LOG_RETENTION_HOURS = 24,           -- مدة الاحتفاظ بالسجلات (ساعة)
    MAX_ERROR_RATE = 10,                -- أقصى عدد أخطاء في الدقيقة
    COOLDOWN_PERIOD = 60,               -- فترة التهدئة (ثانية)
    AUTO_CLEANUP_INTERVAL = 300,        -- تنظيف تلقائي كل 5 دقائق
    CRITICAL_ERROR_THRESHOLD = 5        -- عتبة الأخطاء الحرجة
}

-- ========================================
-- مستويات الأخطاء
-- ========================================

local ERROR_LEVELS = {
    DEBUG = 1,
    INFO = 2,
    WARNING = 3,
    ERROR = 4,
    CRITICAL = 5
}

local ERROR_LEVEL_NAMES = {
    [1] = "DEBUG",
    [2] = "INFO", 
    [3] = "WARNING",
    [4] = "ERROR",
    [5] = "CRITICAL"
}

-- ========================================
-- دوال معالجة الأخطاء
-- ========================================

-- تسجيل خطأ
function ErrorHandler.LogError(level, message, data, source)
    local timestamp = os.time()
    local errorId = Utils.GenerateUniqueId()
    
    local errorEntry = {
        id = errorId,
        level = level,
        levelName = ERROR_LEVEL_NAMES[level] or "UNKNOWN",
        message = message or "Unknown error",
        data = data or {},
        source = source or "Unknown",
        timestamp = timestamp,
        formattedTime = Utils.FormatTime(timestamp),
        stackTrace = debug.traceback()
    }
    
    -- إضافة معلومات إضافية
    errorEntry.serverTime = os.date("%Y-%m-%d %H:%M:%S", timestamp)
    errorEntry.resourceName = GetCurrentResourceName()
    
    -- تسجيل الخطأ
    table.insert(errorLogs, errorEntry)
    
    -- تحديث إحصائيات الأخطاء
    local errorKey = level .. "_" .. (source or "unknown")
    errorCounts[errorKey] = (errorCounts[errorKey] or 0) + 1
    lastErrorTime[errorKey] = timestamp
    
    -- طباعة في وحدة التحكم
    ErrorHandler.PrintError(errorEntry)
    
    -- التحقق من الأخطاء الحرجة
    if level >= ERROR_LEVELS.CRITICAL then
        ErrorHandler.HandleCriticalError(errorEntry)
    end
    
    -- تنظيف السجلات القديمة
    ErrorHandler.CleanupOldLogs()
    
    return errorId
end

-- طباعة الخطأ في وحدة التحكم
function ErrorHandler.PrintError(errorEntry)
    local prefix = string.format("[%s] [%s]", Constants.SCRIPT_NAME, errorEntry.levelName)
    local message = string.format("%s %s: %s", prefix, errorEntry.source, errorEntry.message)
    
    -- اختيار لون حسب مستوى الخطأ
    if errorEntry.level >= ERROR_LEVELS.CRITICAL then
        print("^1" .. message .. "^7") -- أحمر
    elseif errorEntry.level >= ERROR_LEVELS.ERROR then
        print("^3" .. message .. "^7") -- أصفر
    elseif errorEntry.level >= ERROR_LEVELS.WARNING then
        print("^6" .. message .. "^7") -- بنفسجي
    elseif isDebugMode then
        print("^5" .. message .. "^7") -- أزرق
    end
    
    -- طباعة البيانات الإضافية في وضع التطوير
    if isDebugMode and errorEntry.data and next(errorEntry.data) then
        print("^5[DEBUG] Error Data: " .. json.encode(errorEntry.data) .. "^7")
    end
end

-- معالجة الأخطاء الحرجة
function ErrorHandler.HandleCriticalError(errorEntry)
    -- إرسال تنبيه للمطورين
    print("^1[CRITICAL ERROR] " .. errorEntry.message .. "^7")
    
    -- حفظ الخطأ في ملف منفصل
    ErrorHandler.SaveCriticalError(errorEntry)
    
    -- إيقاف العمليات الخطيرة مؤقتاً
    if errorEntry.source == "AntiCheat" then
        -- تفعيل وضع الحماية المشددة
        TriggerEvent("cb_gunshop:enableSafeMode")
    end
end

-- حفظ الأخطاء الحرجة
function ErrorHandler.SaveCriticalError(errorEntry)
    local filename = string.format("critical_error_%s.log", os.date("%Y%m%d_%H%M%S"))
    local content = string.format(
        "Critical Error Report\n" ..
        "====================\n" ..
        "Time: %s\n" ..
        "Source: %s\n" ..
        "Message: %s\n" ..
        "Data: %s\n" ..
        "Stack Trace:\n%s\n",
        errorEntry.formattedTime,
        errorEntry.source,
        errorEntry.message,
        json.encode(errorEntry.data),
        errorEntry.stackTrace
    )
    
    -- في بيئة الإنتاج، يمكن حفظ هذا في قاعدة البيانات
    if Config.Debug then
        print("^1[CRITICAL] Error saved to: " .. filename .. "^7")
    end
end

-- ========================================
-- دوال الاستعلام والإحصائيات
-- ========================================

-- الحصول على جميع الأخطاء
function ErrorHandler.GetAllErrors()
    return errorLogs
end

-- الحصول على أخطاء حسب المستوى
function ErrorHandler.GetErrorsByLevel(level)
    local filtered = {}
    for _, error in ipairs(errorLogs) do
        if error.level == level then
            table.insert(filtered, error)
        end
    end
    return filtered
end

-- الحصول على أخطاء حسب المصدر
function ErrorHandler.GetErrorsBySource(source)
    local filtered = {}
    for _, error in ipairs(errorLogs) do
        if error.source == source then
            table.insert(filtered, error)
        end
    end
    return filtered
end

-- الحصول على إحصائيات الأخطاء
function ErrorHandler.GetErrorStats()
    local stats = {
        totalErrors = #errorLogs,
        errorsByLevel = {},
        errorsBySource = {},
        recentErrors = 0,
        criticalErrors = 0
    }
    
    local oneHourAgo = os.time() - 3600
    
    for _, error in ipairs(errorLogs) do
        -- إحصائيات حسب المستوى
        local levelName = error.levelName
        stats.errorsByLevel[levelName] = (stats.errorsByLevel[levelName] or 0) + 1
        
        -- إحصائيات حسب المصدر
        stats.errorsBySource[error.source] = (stats.errorsBySource[error.source] or 0) + 1
        
        -- الأخطاء الحديثة
        if error.timestamp > oneHourAgo then
            stats.recentErrors = stats.recentErrors + 1
        end
        
        -- الأخطاء الحرجة
        if error.level >= ERROR_LEVELS.CRITICAL then
            stats.criticalErrors = stats.criticalErrors + 1
        end
    end
    
    return stats
end

-- ========================================
-- دوال التنظيف والصيانة
-- ========================================

-- تنظيف السجلات القديمة
function ErrorHandler.CleanupOldLogs()
    local cutoffTime = os.time() - (ERROR_CONFIG.LOG_RETENTION_HOURS * 3600)
    local newLogs = {}
    
    for _, error in ipairs(errorLogs) do
        if error.timestamp > cutoffTime then
            table.insert(newLogs, error)
        end
    end
    
    local removedCount = #errorLogs - #newLogs
    errorLogs = newLogs
    
    if removedCount > 0 and isDebugMode then
        print(string.format("^5[DEBUG] Cleaned up %d old error logs^7", removedCount))
    end
end

-- مسح جميع السجلات
function ErrorHandler.ClearAllLogs()
    errorLogs = {}
    errorCounts = {}
    lastErrorTime = {}
    print("^3[WARNING] All error logs have been cleared^7")
end

-- ========================================
-- دوال الإعداد والتكوين
-- ========================================

-- تفعيل وضع التطوير
function ErrorHandler.EnableDebugMode()
    isDebugMode = true
    ErrorHandler.LogError(ERROR_LEVELS.INFO, "Debug mode enabled", {}, "ErrorHandler")
end

-- إلغاء تفعيل وضع التطوير
function ErrorHandler.DisableDebugMode()
    isDebugMode = false
    ErrorHandler.LogError(ERROR_LEVELS.INFO, "Debug mode disabled", {}, "ErrorHandler")
end

-- التحقق من حالة وضع التطوير
function ErrorHandler.IsDebugMode()
    return isDebugMode
end

-- ========================================
-- دوال مساعدة سريعة
-- ========================================

-- تسجيل خطأ تطوير
function ErrorHandler.Debug(message, data, source)
    if isDebugMode then
        return ErrorHandler.LogError(ERROR_LEVELS.DEBUG, message, data, source)
    end
end

-- تسجيل معلومات
function ErrorHandler.Info(message, data, source)
    return ErrorHandler.LogError(ERROR_LEVELS.INFO, message, data, source)
end

-- تسجيل تحذير
function ErrorHandler.Warning(message, data, source)
    return ErrorHandler.LogError(ERROR_LEVELS.WARNING, message, data, source)
end

-- تسجيل خطأ
function ErrorHandler.Error(message, data, source)
    return ErrorHandler.LogError(ERROR_LEVELS.ERROR, message, data, source)
end

-- تسجيل خطأ حرج
function ErrorHandler.Critical(message, data, source)
    return ErrorHandler.LogError(ERROR_LEVELS.CRITICAL, message, data, source)
end

-- ========================================
-- تهيئة النظام
-- ========================================

-- تهيئة معالج الأخطاء
function ErrorHandler.Initialize(debugMode)
    isDebugMode = debugMode or Config.Debug or false
    
    -- تنظيف دوري للسجلات
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(ERROR_CONFIG.AUTO_CLEANUP_INTERVAL * 1000)
            ErrorHandler.CleanupOldLogs()
        end
    end)
    
    ErrorHandler.Info("Error Handler initialized", {debugMode = isDebugMode}, "ErrorHandler")
end

-- ========================================
-- تصدير النظام
-- ========================================

return ErrorHandler
