-- ========================================
-- CB Gun Shop - Utility Functions
-- دوال مساعدة ومشتركة
-- ========================================

local Utils = {}

-- ========================================
-- دوال التحقق من صحة البيانات
-- ========================================

-- التحقق من صحة معرف المتجر
function Utils.IsValidShopId(shopId)
    if not shopId or type(shopId) ~= "string" then
        return false
    end
    
    for _, shop in ipairs(Config.Shops) do
        if shop.id == shopId then
            return true
        end
    end
    
    return false
end

-- التحقق من صحة اسم السلاح
function Utils.IsValidWeapon(weaponName)
    if not weaponName or type(weaponName) ~= "string" then
        return false
    end
    
    for _, weapon in ipairs(Config.WeaponDatabase) do
        if weapon.name == weaponName then
            return true
        end
    end
    
    return false
end

-- التحقق من صحة كمية الذخيرة
function Utils.IsValidAmmoCount(ammo)
    local ammoCount = tonumber(ammo)
    if not ammoCount then
        return false
    end
    
    return ammoCount >= Config.Weapons.MinAmmo and ammoCount <= Config.Weapons.MaxAmmo
end

-- التحقق من صحة السعر
function Utils.IsValidPrice(price, weaponName)
    local priceNum = tonumber(price)
    if not priceNum or priceNum < 0 then
        return false
    end
    
    -- العثور على السعر الصحيح للسلاح
    for _, weapon in ipairs(Config.WeaponDatabase) do
        if weapon.name == weaponName then
            local correctPrice = weapon.price
            local tolerance = correctPrice * 0.1 -- هامش خطأ 10%
            return math.abs(priceNum - correctPrice) <= tolerance
        end
    end
    
    return false
end

-- ========================================
-- دوال التنسيق والعرض
-- ========================================

-- تنسيق المبلغ المالي
function Utils.FormatMoney(amount)
    if not amount or type(amount) ~= "number" then
        return "0" .. Config.Currency
    end
    
    -- تنسيق الرقم مع فواصل الآلاف
    local formatted = tostring(math.floor(amount))
    local k = 3
    while k < #formatted do
        formatted = formatted:sub(1, #formatted - k) .. "," .. formatted:sub(#formatted - k + 1)
        k = k + 4
    end
    
    return formatted .. Config.Currency
end

-- تنسيق الوقت
function Utils.FormatTime(timestamp)
    if not timestamp then
        return os.date("%Y-%m-%d %H:%M:%S")
    end
    
    return os.date("%Y-%m-%d %H:%M:%S", timestamp)
end

-- تنسيق المسافة
function Utils.FormatDistance(distance)
    if distance < 1000 then
        return string.format("%.1f م", distance)
    else
        return string.format("%.2f كم", distance / 1000)
    end
end

-- ========================================
-- دوال الحسابات
-- ========================================

-- حساب المسافة بين نقطتين
function Utils.GetDistance(pos1, pos2)
    if not pos1 or not pos2 then
        return math.huge
    end
    
    local dx = pos1.x - pos2.x
    local dy = pos1.y - pos2.y
    local dz = pos1.z - pos2.z
    
    return math.sqrt(dx*dx + dy*dy + dz*dz)
end

-- حساب السعر النهائي مع الخصم/الزيادة
function Utils.CalculateFinalPrice(basePrice, discountPercentage)
    if not basePrice or not discountPercentage then
        return basePrice or 0
    end
    
    local finalPrice = basePrice
    
    if discountPercentage > 0 then
        -- خصم
        finalPrice = basePrice * (1 - discountPercentage / 100)
    elseif discountPercentage < 0 then
        -- زيادة
        finalPrice = basePrice * (1 + math.abs(discountPercentage) / 100)
    end
    
    return math.floor(finalPrice)
end

-- حساب سعر الذخيرة الإجمالي
function Utils.CalculateAmmoPrice(ammoCount)
    return (ammoCount or 0) * Config.Weapons.AmmoPrice
end

-- ========================================
-- دوال الوقت والتاريخ
-- ========================================

-- التحقق من ساعات العمل
function Utils.IsWithinWorkingHours(startHour, endHour)
    local currentHour = tonumber(os.date("%H"))
    
    -- إذا كان المتجر مفتوح 24/7
    if startHour == 0 and endHour == 24 then
        return true
    end
    
    -- إذا كانت ساعات العمل عادية (مثل 6-22)
    if startHour < endHour then
        return currentHour >= startHour and currentHour < endHour
    end
    
    -- إذا كانت ساعات العمل تمتد لليوم التالي (مثل 22-6)
    return currentHour >= startHour or currentHour < endHour
end

-- الحصول على التاريخ الحالي
function Utils.GetCurrentDate()
    return os.date("%Y-%m-%d")
end

-- الحصول على الوقت الحالي
function Utils.GetCurrentTime()
    return os.date("%H:%M:%S")
end

-- ========================================
-- دوال النصوص والترجمة
-- ========================================

-- تنظيف النص من الأحرف الخاصة
function Utils.SanitizeText(text)
    if not text or type(text) ~= "string" then
        return ""
    end
    
    -- إزالة الأحرف الخاصة والضارة
    text = text:gsub("[<>\"'&]", "")
    text = text:gsub("%s+", " ") -- تقليل المسافات المتعددة
    text = text:gsub("^%s*(.-)%s*$", "%1") -- إزالة المسافات من البداية والنهاية
    
    return text
end

-- تحويل النص إلى أحرف صغيرة
function Utils.ToLowerCase(text)
    if not text or type(text) ~= "string" then
        return ""
    end
    
    return text:lower()
end

-- ========================================
-- دوال الجداول والبيانات
-- ========================================

-- نسخ جدول بعمق
function Utils.DeepCopy(original)
    local copy
    if type(original) == 'table' then
        copy = {}
        for key, value in next, original, nil do
            copy[Utils.DeepCopy(key)] = Utils.DeepCopy(value)
        end
        setmetatable(copy, Utils.DeepCopy(getmetatable(original)))
    else
        copy = original
    end
    return copy
end

-- دمج جدولين
function Utils.MergeTables(table1, table2)
    local result = Utils.DeepCopy(table1)
    
    for key, value in pairs(table2) do
        result[key] = value
    end
    
    return result
end

-- التحقق من وجود قيمة في جدول
function Utils.TableContains(table, value)
    for _, v in pairs(table) do
        if v == value then
            return true
        end
    end
    return false
end

-- ========================================
-- دوال الأمان
-- ========================================

-- توليد معرف فريد
function Utils.GenerateUniqueId()
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    local id = ""
    
    for i = 1, 16 do
        local rand = math.random(#chars)
        id = id .. chars:sub(rand, rand)
    end
    
    return id .. "_" .. os.time()
end

-- تشفير بسيط للبيانات الحساسة
function Utils.SimpleEncrypt(text, key)
    if not text or not key then
        return text
    end
    
    local result = ""
    local keyLen = #key
    
    for i = 1, #text do
        local char = text:sub(i, i)
        local keyChar = key:sub(((i - 1) % keyLen) + 1, ((i - 1) % keyLen) + 1)
        local encrypted = string.char((char:byte() + keyChar:byte()) % 256)
        result = result .. encrypted
    end
    
    return result
end

-- ========================================
-- دوال التصدير
-- ========================================

-- تصدير جميع الدوال
return Utils
