-- ========================================
-- CB Gun Shop - Debug Fix
-- إصلاح سريع للمشاكل
-- ========================================

-- تحقق من تحميل التكوين
print("^2[DEBUG] Checking Config loading...^7")

if Config then
    print("^2[DEBUG] Config loaded successfully^7")
    
    if Config.Shops then
        print("^2[DEBUG] Found " .. #Config.Shops .. " shops in config^7")
        for i, shop in ipairs(Config.Shops) do
            print("^3[DEBUG] Shop " .. i .. ": " .. shop.name .. " at " .. tostring(shop.coords) .. "^7")
        end
    else
        print("^1[ERROR] Config.Shops is nil!^7")
    end
    
    if Config.WeaponDatabase then
        print("^2[DEBUG] Found " .. #Config.WeaponDatabase .. " weapons in database^7")
    else
        print("^1[ERROR] Config.WeaponDatabase is nil!^7")
    end
else
    print("^1[ERROR] Config is not loaded!^7")
end

-- تحقق من الأحداث
print("^2[DEBUG] Registering test events...^7")

-- حدث اختبار لفتح المتجر
RegisterCommand("testshop", function(source, args)
    local userId = vRP.getUserId({source})
    if userId then
        print("^3[DEBUG] Testing shop for user " .. userId .. "^7")
        
        -- إنشاء بيانات متجر اختبار
        local testShop = {
            id = "test_shop",
            name = "متجر اختبار",
            coords = vector3(0, 0, 0),
            weapons = {
                {name = "weapon_pistol", label = "مسدس", price = 1000, image = "weapon_pistol.png"},
                {name = "weapon_knife", label = "سكين", price = 500, image = "weapon_knife.png"}
            }
        }
        
        local testData = {
            shop = testShop,
            weapons = testShop.weapons,
            playerMoney = 50000,
            serverInfo = {
                name = "CB Store Test",
                logo = "https://via.placeholder.com/60x60"
            }
        }
        
        print("^3[DEBUG] Sending test data to client...^7")
        TriggerClientEvent("cb_gunshop:openUI", source, testData)
    end
end, false)

-- حدث اختبار للتحقق من NUI
RegisterCommand("testnui", function(source, args)
    print("^3[DEBUG] Testing NUI communication...^7")
    TriggerClientEvent("cb_gunshop:testNUI", source)
end, false)

print("^2[DEBUG] Debug commands registered: /testshop, /testnui^7")
