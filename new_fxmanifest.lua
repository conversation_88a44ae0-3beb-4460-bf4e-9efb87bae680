-- ========================================
-- CB Gun Shop - FXManifest
-- سكربت متجر الأسلحة المطور
-- ========================================

fx_version 'cerulean'
game 'gta5'

name 'CB Gun Shop'
description 'Advanced Gun Shop System for FiveM'
author 'CB Team'
version '2.0.0'

-- Dependencies
dependencies {
    'vrp',
    'vrp_mysql'
}

-- Server Scripts
server_scripts {
    '@mysql-async/lib/MySQL.lua',
    '@vrp/lib/utils.lua',
    'new_config.lua',
    'new_server.lua'
}

-- Client Scripts
client_scripts {
    'new_config.lua',
    'new_client.lua'
}

-- Shared Scripts
shared_scripts {
    'new_config.lua'
}

-- UI Files
ui_page 'new_ui/index.html'

files {
    'new_ui/index.html',
    'new_ui/style.css',
    'new_ui/script.js',
    'new_ui/img/*.png',
    'new_ui/img/*.jpg',
    'new_ui/img/*.gif',
    'new_ui/sounds/*.mp3',
    'new_ui/sounds/*.wav'
}

-- Lua 5.4 compatibility
lua54 'yes'

-- Resource metadata
provides {
    'gunshop',
    'weaponshop',
    'armory'
}

-- Server exports
server_exports {
    'GetShopData',
    'GetPlayerPurchases',
    'AddWeaponToShop',
    'RemoveWeaponFromShop',
    'SetShopStatus',
    'GetShopLogs'
}

-- Client exports
client_exports {
    'GetCurrentShop',
    'IsShopUIOpen',
    'ForceCloseShopUI',
    'GetNearbyShops'
}
