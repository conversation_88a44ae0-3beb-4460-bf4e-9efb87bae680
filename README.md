# CB Gun Shop - نظام متجر الأسلحة المطور

## 🔫 نظرة عامة

CB Gun Shop هو نظام متجر أسلحة متطور ومحسن لخوادم FiveM، مصمم خصيصاً للمجتمعات العربية. يوفر النظام واجهة مستخدم حديثة وآمنة مع ميزات متقدمة لإدارة الأسلحة والمتاجر.

## ✨ المميزات

### 🎨 واجهة مستخدم حديثة
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية
- رسوم متحركة سلسة
- تصميم متوافق مع الهواتف المحمولة

### 🔒 نظام أمان متقدم
- مكافحة الغش والتلاعب
- تسجيل جميع المعاملات
- حدود شراء يومية
- نظام صلاحيات متقدم

### ⚡ أداء محسن
- استهلاك موارد منخفض
- نظام تحقق ذكي من المسافة
- تحميل تدريجي للموارد
- تحسينات للخوادم عالية الكثافة

### 🛠️ قابلية التخصيص
- إعدادات مرنة وسهلة التعديل
- دعم فئات أسلحة متعددة
- نظام متاجر متعددة
- إعدادات مخصصة لكل متجر

## 📋 المتطلبات

- **FiveM Server** (أحدث إصدار)
- **vRP Framework** (الإصدار 2.0 أو أحدث)
- **MySQL Database** (MariaDB مُفضل)
- **mysql-async** (مورد قاعدة البيانات)

## 🚀 التثبيت

### الخطوة 1: تحميل الملفات
```bash
# انسخ جميع الملفات إلى مجلد الموارد
cp -r CB_GunShop /path/to/your/server/resources/
```

### الخطوة 2: إعداد قاعدة البيانات
```sql
-- إنشاء جدول سجلات المتجر (اختياري)
CREATE TABLE IF NOT EXISTS `gunshop_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `weapon_name` varchar(50) NOT NULL,
    `shop_id` varchar(50) NOT NULL,
    `price` int(11) NOT NULL,
    `ammo` int(11) NOT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

-- إنشاء جدول حدود الشراء (اختياري)
CREATE TABLE IF NOT EXISTS `gunshop_purchases` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `shop_id` varchar(50) NOT NULL,
    `purchase_date` date NOT NULL,
    `purchase_count` int(11) DEFAULT 1,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_shop_date` (`user_id`, `shop_id`, `purchase_date`)
);
```

### الخطوة 3: إضافة المورد
```lua
-- في ملف server.cfg
ensure CB_GunShop
```

### الخطوة 4: إعداد الصلاحيات
```lua
-- في ملف groups.lua الخاص بـ vRP
["citizen"] = {
    "citizen.basic",
    "gunshop.access"
},

["police"] = {
    _config = {
        title = "Police Officer",
        gtype = "job"
    },
    "police.officer",
    "police.armory"
},

["vip"] = {
    "vip.premium",
    "vip.gold"
},

["admin"] = {
    "server.owner",
    "blackmarket.access",
    "criminal.advanced"
}
```

## ⚙️ التكوين

### إعدادات أساسية
```lua
-- في ملف new_config.lua
Config.Locale = 'ar' -- اللغة
Config.Currency = '$' -- رمز العملة
Config.Debug = false -- وضع التطوير

-- إعدادات الأداء
Config.Performance = {
    CheckDistance = 15.0,      -- مسافة التحقق
    MarkerDistance = 8.0,      -- مسافة الماركر
    InteractionDistance = 2.5, -- مسافة التفاعل
    RefreshRate = 500         -- معدل التحديث
}
```

### إضافة متجر جديد
```lua
{
    id = "my_custom_shop",
    name = "متجر مخصص",
    coords = vector3(x, y, z),
    heading = 0.0,
    
    blip = {
        sprite = 110,
        color = 1,
        scale = 0.8
    },
    
    permissions = {
        "gunshop.access"
    },
    
    allowedCategories = {"pistol", "rifle"},
    
    settings = {
        requireLicense = true,
        maxPurchasePerDay = 5,
        discountPercentage = 0,
        openHours = {startHour = 6, endHour = 22}
    }
}
```

### إضافة سلاح جديد
```lua
{
    name = "weapon_newgun",
    label = "سلاح جديد",
    category = "pistol",
    price = 5000,
    image = "weapon_newgun.png",
    ammo = "AMMO_PISTOL",
    stats = {
        damage = 75,
        accuracy = 80,
        range = 50,
        fireRate = 70
    },
    description = "وصف السلاح",
    legal = true,
    vip = false
}
```

## 🎮 طريقة الاستخدام

### للاعبين
1. **الوصول للمتجر**: اقترب من أي متجر أسلحة واضغط `E`
2. **البحث**: استخدم شريط البحث للعثور على أسلحة محددة
3. **التصفية**: استخدم أزرار الفئات لتصفية الأسلحة
4. **الشراء**: انقر على السلاح المطلوب واختر كمية الذخيرة
5. **التأكيد**: راجع التفاصيل واضغط "تأكيد الشراء"

### للمطورين
```lua
-- الحصول على المتجر الحالي
local currentShop = exports.CB_GunShop:GetCurrentShop()

-- التحقق من حالة الواجهة
local isOpen = exports.CB_GunShop:IsShopUIOpen()

-- إغلاق الواجهة قسرياً
exports.CB_GunShop:ForceCloseShopUI()
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

**المتجر لا يفتح:**
- تأكد من وجود الصلاحيات المطلوبة
- تحقق من ساعات العمل
- راجع وحدة التحكم للأخطاء

**الأسلحة لا تظهر:**
- تأكد من صحة مسارات الصور
- تحقق من إعدادات الفئات المسموحة
- راجع ملف التكوين

**مشاكل في الأداء:**
- قلل من `CheckDistance` في الإعدادات
- زد من `RefreshRate`
- تأكد من تحديث الخادم

### تفعيل وضع التطوير
```lua
Config.Debug = true -- في ملف new_config.lua
```

## 📝 سجل التغييرات

### الإصدار 2.0.0
- إعادة كتابة كاملة للنظام
- واجهة مستخدم جديدة ومحسنة
- نظام أمان متقدم
- تحسينات في الأداء
- دعم أفضل للغة العربية

### الإصدار 1.0.0
- الإصدار الأولي

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 📞 الدعم

- **Discord**: [رابط الديسكورد]
- **GitHub Issues**: [رابط المشاكل]
- **التوثيق**: [رابط التوثيق]

## 🙏 شكر خاص

- فريق FiveM للمنصة الرائعة
- مجتمع vRP للإطار المتميز
- جميع المساهمين والمختبرين

---

**تم تطويره بـ ❤️ من قبل فريق CB**
