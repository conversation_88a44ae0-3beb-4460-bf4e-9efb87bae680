<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر الأسلحة - CB Gun Shop</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="container" class="hidden">
        <div class="shop-wrapper">
            <!-- Header -->
            <div class="shop-header">
                <div class="header-left">
                    <img id="serverLogo" src="" alt="Server Logo" class="server-logo">
                    <div class="shop-info">
                        <h1 id="shopName">متجر الأسلحة</h1>
                        <p id="serverName">CB Gun Shop</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="money-display">
                        <i class="money-icon">💰</i>
                        <span class="money-label">رصيدك:</span>
                        <span id="playerMoney" class="money-amount">0</span>
                        <span class="currency">$</span>
                    </div>
                    <button id="closeBtn" class="close-btn">
                        <i>✕</i>
                    </button>
                </div>
            </div>

            <!-- Categories -->
            <div class="categories-container">
                <div class="categories-wrapper">
                    <button class="category-btn active" data-category="pistol">
                        <i class="category-icon">🔫</i>
                        <span>مسدسات</span>
                    </button>
                    <button class="category-btn" data-category="smg">
                        <i class="category-icon">🔫</i>
                        <span>رشاشات</span>
                    </button>
                    <button class="category-btn" data-category="rifle">
                        <i class="category-icon">🔫</i>
                        <span>بنادق</span>
                    </button>
                    <button class="category-btn" data-category="sniper">
                        <i class="category-icon">🎯</i>
                        <span>قناصات</span>
                    </button>
                    <button class="category-btn" data-category="shotgun">
                        <i class="category-icon">💥</i>
                        <span>خرطوش</span>
                    </button>
                </div>
            </div>

            <!-- Weapons Container -->
            <div class="weapons-section">
                <div class="section-header">
                    <h2 id="categoryTitle">المسدسات</h2>
                    <div class="weapons-count">
                        <span id="weaponsCount">0</span>
                        <span>سلاح متاح</span>
                    </div>
                </div>
                
                <div class="weapons-container" id="weaponsContainer">
                    <!-- الأسلحة ستظهر هنا -->
                </div>
            </div>

            <!-- Purchase Modal -->
            <div id="purchaseModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تأكيد الشراء</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="purchase-details">
                            <div class="weapon-preview">
                                <img id="modalWeaponImage" src="" alt="Weapon">
                                <div class="weapon-info">
                                    <h4 id="modalWeaponName">اسم السلاح</h4>
                                    <p id="modalWeaponDesc">وصف السلاح</p>
                                </div>
                            </div>
                            
                            <div class="purchase-options">
                                <div class="ammo-selection">
                                    <label for="ammoSlider">كمية الذخيرة:</label>
                                    <div class="slider-container">
                                        <input type="range" id="ammoSlider" min="0" max="500" value="100">
                                        <div class="slider-values">
                                            <span>0</span>
                                            <span id="ammoValue">100</span>
                                            <span>500</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="price-breakdown">
                                    <div class="price-row">
                                        <span>سعر السلاح:</span>
                                        <span id="weaponPrice">0$</span>
                                    </div>
                                    <div class="price-row">
                                        <span>سعر الذخيرة:</span>
                                        <span id="ammoPrice">0$</span>
                                    </div>
                                    <div class="price-row total">
                                        <span>المجموع:</span>
                                        <span id="totalPrice">0$</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="confirmPurchase" class="btn-confirm">تأكيد الشراء</button>
                        <button id="cancelPurchase" class="btn-cancel">إلغاء</button>
                    </div>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div id="loadingOverlay" class="loading-overlay hidden">
                <div class="loading-spinner"></div>
                <p>جاري المعالجة...</p>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <script src="script.js"></script>
</body>
</html>